"""
Pydantic schemas for dynamic record management.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union
from uuid import UUID

from pydantic import BaseModel, Field, field_validator

from app.schemas.common import BaseSchema, SchemaTimestampMixin

class RecordSource(str, Enum):
    """Source of the record creation."""
    USER = "user"
    WORKFLOW = "workflow"

class DynamicRecordCreate(BaseSchema):
    """Schema for creating a new dynamic record."""
    schema_name: str = Field(..., min_length=1, max_length=100, description="Schema name")
    data: Dict[str, Any] = Field(..., description="Record data")
    created_by: Union[int,str] = Field(..., description="User ID who created the record")
    created_by_source: RecordSource = Field(..., description="Source of the creation (user or workflow)")

    @field_validator('data')
    def validate_data_not_empty(cls, v):
        if not v:
            raise ValueError('Record data cannot be empty')
        return v


class DynamicRecordUpdate(BaseSchema):
    """Schema for updating an existing dynamic record."""
    data: Dict[str, Any] = Field(..., description="Updated record data")


    @field_validator('data')
    def validate_data_not_empty(cls, v):
        if not v:
            raise ValueError('Record data cannot be empty')
        return v


class DynamicRecord(BaseSchema, SchemaTimestampMixin):
    """Dynamic record response model."""
    id: str = Field(..., description="Record ID")


class DynamicRecordList(BaseSchema):
    """Dynamic record list response."""
    records: List[Any]
    total: int
    page: int
    page_size: int
    schema_name: str


class DynamicRecordFilter(BaseSchema):
    """Filter options for dynamic records."""
    field: str = Field(..., description="Field name to filter by")
    operator: str = Field(..., description="Filter operator (eq, ne, gt, gte, lt, lte, in, nin, contains)")
    value: Any = Field(..., description="Filter value")

    @field_validator('operator')
    def validate_operator(cls, v):
        allowed_operators = ['eq', 'ne', 'gt', 'gte', 'lt', 'lte', 'in', 'nin', 'contains', 'regex']
        if v not in allowed_operators:
            raise ValueError(f'Operator must be one of: {", ".join(allowed_operators)}')
        return v


class DynamicRecordQuery(BaseSchema):
    """Query parameters for dynamic records."""
    schema_name: str = Field(..., description="Name of the schema to query")
    filters: Optional[List[DynamicRecordFilter]] = Field(None, description="Filter conditions")
    sort_by: Optional[str] = Field(None, description="Field to sort by")
    sort_order: Optional[str] = Field("asc", description="Sort order (asc or desc)")
    page: int = Field(1, ge=1, description="Page number")
    page_size: int = Field(20, ge=1, le=100, description="Page size")

    @field_validator('sort_order')
    def validate_sort_order(cls, v):
        if v not in ['asc', 'desc']:
            raise ValueError('Sort order must be "asc" or "desc"')
        return v


class DynamicRecordValidationError(BaseModel):
    """Dynamic record validation error details."""
    field: str
    message: str
    invalid_value: Optional[Any] = None


class DynamicRecordValidationResponse(BaseModel):
    """Dynamic record validation response."""
    valid: bool
    errors: List[DynamicRecordValidationError] = Field(default_factory=list)
    record_id: Optional[str] = None
    schema_name: Optional[str] = None


class DynamicRecordBulkCreate(BaseSchema):
    """Schema for bulk creating dynamic records."""
    schema_name: str = Field(..., min_length=1, max_length=100, description="Schema name")
    records: List[Dict[str, Any]] = Field(..., description="Records data")

    @field_validator('records')
    def validate_records(cls, v):
        if len(v) > 100:
            raise ValueError('No more than 100 records can be created at once')
        for i, record in enumerate(v):
            if not record:
                raise ValueError(f'Record at index {i} cannot be empty')
        return v


class DynamicRecordBulkResponse(BaseSchema):
    """Response for bulk operations."""
    successful: List[DynamicRecord] = Field(default_factory=list, description="Successfully processed records")
    failed: List[Dict[str, Any]] = Field(default_factory=list, description="Failed records with errors")
    total_processed: int = Field(0, description="Total number of records processed")
    success_count: int = Field(0, description="Number of successful operations")
    failure_count: int = Field(0, description="Number of failed operations")


class DynamicRecordStats(BaseSchema):
    """Statistics for dynamic records in a schema."""
    schema_name: str
    total_records: int
    created_today: int
    updated_today: int
    field_statistics: Dict[str, Any] = Field(default_factory=dict, description="Statistics per field")


class ContactRecord(BaseSchema):
    """Specialized schema for Contact records with validation."""
    id: Optional[str] = Field(None, description="Contact ID (auto-generated)")
    first_name: str = Field(..., min_length=1, max_length=100, description="First name")
    last_name: str = Field(..., min_length=1, max_length=100, description="Last name")
    email: str = Field(..., description="Email address")
    phone: str = Field(..., description="Phone number")
    
    # Additional fields can be added dynamically
    additional_data: Optional[Dict[str, Any]] = Field(None, description="Additional custom fields")

    @field_validator('email')
    def validate_email(cls, v):
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, v):
            raise ValueError('Invalid email format')
        return v.lower()

    @field_validator('phone')
    def validate_phone(cls, v):
        import re
        # Remove all non-digit characters
        digits_only = re.sub(r'\D', '', v)
        if len(digits_only) < 10:
            raise ValueError('Phone number must contain at least 10 digits')
        return v


class ContactRecordCreate(BaseSchema):
    """Schema for creating contact records."""
    first_name: str = Field(..., min_length=1, max_length=100)
    last_name: str = Field(..., min_length=1, max_length=100)
    email: str = Field(...)
    phone: str = Field(...)
    additional_data: Optional[Dict[str, Any]] = Field(None)
    created_by: int = Field(...)

    @field_validator('email')
    def validate_email(cls, v):
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, v):
            raise ValueError('Invalid email format')
        return v.lower()

    @field_validator('phone')
    def validate_phone(cls, v):
        import re
        digits_only = re.sub(r'\D', '', v)
        if len(digits_only) < 10:
            raise ValueError('Phone number must contain at least 10 digits')
        return v


class ContactRecordUpdate(BaseSchema):
    """Schema for updating contact records."""
    first_name: Optional[str] = Field(None, min_length=1, max_length=100)
    last_name: Optional[str] = Field(None, min_length=1, max_length=100)
    email: Optional[str] = Field(None)
    phone: Optional[str] = Field(None)
    additional_data: Optional[Dict[str, Any]] = Field(None)
    updated_by: int = Field(...)

    @field_validator('email')
    def validate_email(cls, v):
        if v is not None:
            import re
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, v):
                raise ValueError('Invalid email format')
            return v.lower()
        return v

    @field_validator('phone')
    def validate_phone(cls, v):
        if v is not None:
            import re
            digits_only = re.sub(r'\D', '', v)
            if len(digits_only) < 10:
                raise ValueError('Phone number must contain at least 10 digits')
        return v
