"""
Pydantic schemas for dynamic schema management.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union
from typing import Literal
from uuid import UUID

from pydantic import BaseModel, Field, field_validator, model_validator

from app.schemas.common import BaseSchema, IDMixin, SchemaTimestampMixin
from app.schemas.dynamic_record import RecordSource
from app.utils.constant import AUDIT_FIELD_NAMES


class FieldType(str, Enum):
    """Supported field types for dynamic schemas."""
    STRING = "string"
    NUMBER = "number"
    INTEGER = "integer"
    BOOLEAN = "boolean"
    DATE = "date"
    DATETIME = "datetime"
    EMAIL = "email"
    PHONE = "phone"
    URL = "url"
    OBJECT = "object"
    OBJECTID = "objectId"
    ARRAY = "array"


class ValidationRule(BaseModel):
    """Validation rule for schema fields."""
    min_length: Optional[int] = Field(None, ge=0, description="Minimum string length")
    max_length: Optional[int] = Field(None, ge=0, description="Maximum string length")
    min_value: Optional[Union[int, float]] = Field(None, description="Minimum numeric value")
    max_value: Optional[Union[int, float]] = Field(None, description="Maximum numeric value")
    pattern: Optional[str] = Field(None, description="Regex pattern for string validation")
    enum_values: Optional[List[str]] = Field(None, description="Allowed enum values")
    required: bool = Field(True, description="Whether field is required")
    unique: bool = Field(False, description="Whether field values must be unique")

    @field_validator('max_length')
    def validate_max_length(cls, v, info):
        min_length = info.data.get('min_length')
        if v is not None and min_length is not None:
            if v < min_length:
                raise ValueError(f'max_length ({v}) must be greater than or equal to min_length ({min_length})')
        return v
        
    @field_validator('max_value')
    def validate_max_value(cls, v, info):
        min_value = info.data.get('min_value')
        if v is not None and min_value is not None:
            if v < min_value:
                raise ValueError(f'max_value ({v}) must be greater than or equal to min_value ({min_value})')
        return v
    
    @model_validator(mode='after')
    def validate_field_type_compatibility(cls, values):
        """Validate that rules are compatible with field types."""
        # Additional validation can be added here to check field type compatibility
        # For example, ensuring pattern is only used with string fields
        
        # This will be called during schema validation and provide clear errors
        pattern = values.pattern
        enum_values = values.enum_values
        
        if pattern is not None and not isinstance(pattern, str):
            raise ValueError(f"Pattern must be a string, got {type(pattern).__name__}: {pattern}")
            
        if enum_values is not None and not isinstance(enum_values, list):
            raise ValueError(f"enum_values must be a list, got {type(enum_values).__name__}: {enum_values}")
            
        return values


class SchemaField(BaseModel):
    """Schema field definition."""
    name: str = Field(..., min_length=1, max_length=100, description="Field name")
    display_name: str = Field(..., min_length=1, max_length=200, description="Human-readable field name")
    description: Optional[str] = Field(None, max_length=500, description="Field description")
    field_type: FieldType = Field(..., description="Field data type")
    validation_rules: ValidationRule = Field(default_factory=ValidationRule, description="Validation rules")
    default_value: Optional[Any] = Field(None, description="Default field value")
    is_default_field: bool = Field(False, description="Whether this is a system default field")
    order: int = Field(0, ge=0, description="Display order")

    @field_validator('name')
    def validate_name(cls, v):
        # Allow system field names that start with underscore
        if v.startswith('_') and v not in AUDIT_FIELD_NAMES:
            raise ValueError("Field name cannot start with underscore unless it's a system field")
        # Field name must be valid identifier
        if not v.replace('_', '').isalnum():
            raise ValueError('Field name must contain only alphanumeric characters and underscores')
        return v.lower()

    @model_validator(mode='after')
    def validate_default_value_type(cls, values):
        field_type = values.field_type
        default_value = values.default_value
        
        if default_value is not None and field_type:
            # Validate default value matches field type
            if field_type == FieldType.STRING and not isinstance(default_value, str):
                raise ValueError('Default value must be string for string fields')
            elif field_type == FieldType.NUMBER and not isinstance(default_value, (int, float)):
                raise ValueError('Default value must be number for number fields')
            elif field_type == FieldType.INTEGER and not isinstance(default_value, int):
                raise ValueError('Default value must be integer for integer fields')
            elif field_type == FieldType.BOOLEAN and not isinstance(default_value, bool):
                raise ValueError('Default value must be boolean for boolean fields')
        
        return values


class SchemaDefinitionCreate(BaseSchema):
    """Schema for creating a new dynamic schema."""
    name: str = Field(..., min_length=1, max_length=100, description="Schema name")
    display_name: str = Field(..., min_length=1, max_length=200, description="Human-readable schema name")
    description: Optional[str] = Field(None, max_length=1000, description="Schema description")
    fields: List[SchemaField] = Field(..., description="Schema fields")

class SchemaDefinitionCreateDB(SchemaDefinitionCreate):
    """Schema for creating a new dynamic schema in the database."""
    created_by: Optional[str] = Field(None, description="User ID who created the schema")
    created_by_source: Optional[RecordSource] = Field(None, description="Source of the creation (user or workflow)")

    @field_validator('name')
    def validate_name(cls, v):
        # Schema name must be valid identifier
        if not v.replace('_', '').isalnum():
            raise ValueError('Schema name must contain only alphanumeric characters and underscores')
        if v.startswith('_'):
            raise ValueError('Schema name cannot start with underscore')
        return v.lower()

    @field_validator('fields')
    def validate_fields(cls, v):
        # Check for duplicate field names
        field_names = [field.name for field in v]
        if len(field_names) != len(set(field_names)):
            raise ValueError('Duplicate field names are not allowed')
        
        # Ensure at least one field is required
        if not any(field.validation_rules.required for field in v):
            raise ValueError('At least one field must be required')
        
        return v
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "name": "product",
                "display_name": "Products",
                "description": "Product information",
                "fields": [
                    {
                        "name": "name",
                        "display_name": "Product Name",
                        "description": "Product name",
                        "field_type": "string",
                        "validation_rules": {
                            "required": True,
                            "unique": False,
                            "min_length": 1,
                            "max_length": 100,
                            "min_value": None,
                            "max_value": None,
                            "pattern": None,
                            "enum_values": None
                        },
                        "default_value": None,
                        "is_default_field": False,
                        "order": 1
                    },
                    {
                        "name": "reference",
                        "display_name": "Reference",
                        "description": "Reference number",
                        "field_type": "string",
                        "validation_rules": {
                            "required": True,
                            "unique": True,
                            "min_length": 1,
                            "max_length": 100,
                            "min_value": None,
                            "max_value": None,
                            "pattern": None,
                            "enum_values": None
                        },
                        "default_value": None,
                        "is_default_field": False,
                        "order": 2
                    },
                    {
                        "name": "price",
                        "display_name": "Bese Price",
                        "description": "Base price of the product",
                        "field_type": "number",
                        "validation_rules": {
                            "required": True,
                            "unique": False,
                            "min_length": 1,
                            "max_length": 100,
                            "min_value": None,
                            "max_value": None,
                            "pattern": None,
                            "enum_values": None
                        },
                        "default_value": None,
                        "is_default_field": False,
                        "order": 2
                    }
                ],
                "created_by": 1
            }
        }
    }


class SchemaDefinitionUpdate(BaseSchema):
    """Schema for updating an existing dynamic schema."""
    display_name: Optional[str] = Field(None, min_length=1, max_length=200, description="Human-readable schema name")
    description: Optional[str] = Field(None, max_length=1000, description="Schema description")
    fields: Optional[List[SchemaField]] = Field(None, description="Schema fields")
    updated_by: Optional[int] = Field(..., description="User ID who updated the schema")

    @field_validator('fields')
    def validate_fields(cls, v):
        if v is not None:
            # Check for duplicate field names
            field_names = [field.name for field in v]
            if len(field_names) != len(set(field_names)):
                raise ValueError('Duplicate field names are not allowed')
            
            # Ensure at least one field is required
            if not any(field.validation_rules.required for field in v):
                raise ValueError('At least one field must be required')
        
        return v


class SchemaDefinition(BaseSchema, IDMixin, SchemaTimestampMixin):
    """Schema response model."""
    name: str
    display_name: str
    description: Optional[str]
    fields: List[SchemaField]
    version: int
    _created_by: Optional[str]
    _updated_by: Optional[str]
    is_system_schema: bool = Field(False, description="Whether this is a system-defined schema")


class SchemaDefinitionList(BaseSchema):
    """Schema list response."""
    schemas: List[SchemaDefinition]
    total: int
    page: int
    page_size: int


class SchemaValidationError(BaseModel):
    """Schema validation error details."""
    field: str
    message: str
    invalid_value: Optional[Any] = None


class SchemaValidationResponse(BaseModel):
    """Schema validation response."""
    valid: bool
    errors: List[SchemaValidationError] = Field(default_factory=list)
    schema_name: Optional[str] = None
