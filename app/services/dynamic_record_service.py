"""
Business logic service for dynamic record management.
"""

from typing import Dict, Any

from motor.motor_asyncio import AsyncIOMotorDatabase

from app.repositories.dynamic_record_repository import DynamicRecordRepository
from app.services.schema_service import SchemaService
from app.services.schema_validation_service import SchemaValidationService
from app.schemas.dynamic_record import (
    DynamicRecordCreate, DynamicRecordUpdate, DynamicRecord, DynamicRecordList,
    DynamicRecordQuery, DynamicRecordValidationResponse, DynamicRecordBulkCreate,
    DynamicRecordBulkResponse, DynamicRecordStats
)
from app.utils.exceptions import NotFoundError
from app.utils.logging import get_logger

logger = get_logger("services.dynamic_record")


class DynamicRecordService:
    """Service for dynamic record management with schema validation."""
    
    def __init__(self, db: AsyncIOMotorDatabase, schema_service: SchemaService, schema_name: str=""):
        """
        Initialize service with database and schema service.
        
        Args:
            db: MongoDB database instance
            schema_service: Schema service instance
        """
        self.repository = DynamicRecordRepository(db, schema_name=schema_name)
        self.schema_service = schema_service
        self.schema_name = schema_name
        
    
    # async def initialize(self) -> None:
    #     """Initialize the service."""
    #     await self.repository.ensure_indexes()
    
    async def create_record(self, record_data: DynamicRecordCreate) -> Any:
        """
        Create a new dynamic record.
        
        Args:
            record_data: Record creation data
            
        Returns:
            DynamicRecord: Created record
            
        Raises:
            NotFoundError: If schema not found
            ValidationError: If data validation fails
        """
        # Check if schema is "event"
        if record_data.schema_name.lower() == "event":
            raise ValueError("Cannot create records for schema 'event'")

        # Get schema
        schema = await self.schema_service.get_schema_by_name(record_data.schema_name)

        # Sanitize and apply default values
        sanitized_data = SchemaValidationService.sanitize_data(record_data.data, schema.fields)
        data_with_defaults = SchemaValidationService.apply_default_values(sanitized_data, schema.fields)

        # # Validate data against schema
        # validation_result = SchemaValidationService.validate_data_against_schema(
        #     data_with_defaults, schema.fields
        # )
        
        # if not validation_result.valid:
        #     error_messages = [f"{error.field}: {error.message}" for error in validation_result.errors]
        #     raise ValidationError(f"Data validation failed: {'; '.join(error_messages)}")
        
        # Create record with validated data
        record_data.data = data_with_defaults
        return await self.repository.create(record_data)
        
        # return self._doc_to_record(created_doc)
    
    async def get_record_by_id(self, record_id: str) -> Dict[str, Any]:
        """
        Get record by ID.
        
        Args:
            record_id: Record ID
            
        Returns:
            DynamicRecord: Record instance
            
        Raises:
            NotFoundError: If record not found
        """
        doc = await self.repository.get_by_id(record_id)
        if not doc:
            raise NotFoundError(f"Record with ID '{record_id}' not found in schema '{self.schema_name}'")
        return doc
    
    async def get_records(self, query: DynamicRecordQuery) -> DynamicRecordList:
        """
        Get multiple records with filtering and pagination.
        
        Args:
            query: Query parameters
            
        Returns:
            DynamicRecordList: List of records with pagination info
            
        Raises:
            NotFoundError: If schema not found
        """

        # Verify schema exists
        await self.schema_service.get_schema_by_name(query.schema_name)

        # Convert query filters to repository format
        filters = None
        if query.filters:
            filters = [
                {
                    "field": f.field,
                    "operator": f.operator,
                    "value": f.value
                }
                for f in query.filters
            ]

        # Calculate skip
        skip = (query.page - 1) * query.page_size
        
        # Get sort order
        sort_order = 1 if query.sort_order == "asc" else -1
        
        # Get records and count
        docs = await self.repository.get_multi(
            skip=skip,
            limit=query.page_size,
            filters=filters,
            sort_by=query.sort_by or "_created_on",
            sort_order=sort_order
        )

        total = await self.repository.count(filters=filters)

        # Convert to response models
        records = [doc for doc in docs]
        return DynamicRecordList(
            records=records,
            total=total,
            page=query.page,
            page_size=query.page_size,
            schema_name=query.schema_name
        )
    
    async def update_record(self, record_id: str, update_data: DynamicRecordUpdate) -> Dict[str, Any]:
        """
        Update an existing record.
        
        Args:
            record_id: Record ID
            update_data: Update data
            
        Returns:
            DynamicRecord: Updated record
            
        Raises:
            NotFoundError: If record or schema not found
            ValidationError: If data validation fails
        """
        
        # Check if schema is "event"
        if self.schema_name == "event":
            raise ValueError("Cannot update records for schema 'event'")

        # Get schema
        schema = await self.schema_service.get_schema_by_name(self.schema_name)
        merged_data = {}

        # Sanitize and apply default values
        sanitized_data = SchemaValidationService.sanitize_data(merged_data, schema.fields)

        # Update record with validated data
        update_data.data = sanitized_data
        updated_doc = await self.repository.update(record_id, update_data)
        
        if not updated_doc:
            raise NotFoundError(f"Record with ID '{record_id}' not found in schema '{self.schema_name}'")
        return updated_doc
    
    async def delete_record(self, record_id: str) -> bool:
        """
        Delete a record.
        
        Args:
            record_id: Record ID
            
        Returns:
            bool: True if deleted
            
        Raises:
            NotFoundError: If record not found
        """
        # Verify record exists
        existing = await self.repository.exists(record_id)
        if not existing:
            raise NotFoundError(f"Record with ID '{record_id}' not found in schema '{self.schema_name}'")
        
        return await self.repository.delete(record_id)
    
    #TODO: NKKK - future reference
    async def validate_record_data(self, data: Dict[str, Any]) -> DynamicRecordValidationResponse:
        """
        Validate record data against schema.
        
        Args:
            schema_name: Schema name
            data: Data to validate
            
        Returns:
            DynamicRecordValidationResponse: Validation result
            
        Raises:
            NotFoundError: If schema not found
        """
        # Get schema
        schema = await self.schema_service.get_schema_by_name(self.schema_name)
        
        # Sanitize and apply default values
        sanitized_data = SchemaValidationService.sanitize_data(data, schema.fields)
        data_with_defaults = SchemaValidationService.apply_default_values(sanitized_data, schema.fields)
        
        # Validate data
        validation_result = SchemaValidationService.validate_data_against_schema(
            data_with_defaults, schema.fields
        )
        
        validation_result.schema_name = self.schema_name
        return validation_result
    
    async def bulk_create_records(self, bulk_data: DynamicRecordBulkCreate) -> DynamicRecordBulkResponse:
        """
        Create multiple records in bulk.
        
        Args:
            bulk_data: Bulk creation data
            
        Returns:
            DynamicRecordBulkResponse: Bulk operation result
            
        Raises:
            NotFoundError: If schema not found
        """
        # Get schema
        schema = await self.schema_service.get_schema_by_name(bulk_data.schema_name)
        
        successful_records = []
        failed_records = []
        
        # Validate and prepare each record
        validated_records = []
        for i, record_data in enumerate(bulk_data.records):
            try:
                # Sanitize and apply default values
                sanitized_data = SchemaValidationService.sanitize_data(record_data, schema.fields)
                data_with_defaults = SchemaValidationService.apply_default_values(sanitized_data, schema.fields)
                
                # Validate data
                validation_result = SchemaValidationService.validate_data_against_schema(
                    data_with_defaults, schema.fields
                )
                
                if validation_result.valid:
                    validated_records.append(data_with_defaults)
                else:
                    error_messages = [f"{error.field}: {error.message}" for error in validation_result.errors]
                    failed_records.append({
                        "index": i,
                        "data": record_data,
                        "errors": error_messages
                    })
            except Exception as e:
                failed_records.append({
                    "index": i,
                    "data": record_data,
                    "errors": [str(e)]
                })
        
        # Create valid records in bulk
        if validated_records:
            try:
                created_docs = await self.repository.bulk_create(validated_records)
                successful_records = [self._doc_to_record(doc) for doc in created_docs]
            except Exception as e:
                # If bulk creation fails, mark all as failed
                for i, record_data in enumerate(validated_records):
                    failed_records.append({
                        "index": len(failed_records) + i,
                        "data": record_data,
                        "errors": [f"Bulk creation failed: {str(e)}"]
                    })
        
        return DynamicRecordBulkResponse(
            successful=successful_records,
            failed=failed_records,
            total_processed=len(bulk_data.records),
            success_count=len(successful_records),
            failure_count=len(failed_records)
        )
    
    async def get_schema_stats(self, schema_name: str) -> DynamicRecordStats:
        """
        Get statistics for records in a schema.
        
        Args:
            schema_name: Schema name
            
        Returns:
            DynamicRecordStats: Statistics data
            
        Raises:
            NotFoundError: If schema not found
        """
        # Verify schema exists
        await self.schema_service.get_schema_by_name(schema_name)
        
        # Get statistics
        stats_data = await self.repository.get_schema_stats()
        return DynamicRecordStats(**stats_data)
    
    async def delete_all_records(self, schema_name: str) -> int:
        """
        Delete all records for a schema.
        
        Args:
            schema_name: Schema name
            
        Returns:
            int: Number of deleted records
            
        Raises:
            NotFoundError: If schema not found
        """
        # Verify schema exists
        await self.schema_service.get_schema_by_name(schema_name)
        
        return await self.repository.delete_all_by_schema()
    
    def _doc_to_record(self, doc: Dict[str, Any]) -> DynamicRecord:
        """
        Convert database document to DynamicRecord model.
        
        Args:
            doc: Database document
            
        Returns:
            DynamicRecord: Record model
        """ 
        return DynamicRecord(
            id=doc["id"],
            _created_on=doc["_created_on"],
            _updated_on=doc["_updated_on"]
        )
