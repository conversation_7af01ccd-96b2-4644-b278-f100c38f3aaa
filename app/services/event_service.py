"""
Event service for managing event processing operations.
"""

from typing import Dict, List, Optional, Any, cast
from uuid import uuid4

from motor.motor_asyncio import AsyncIOMotorDatabase

from app.repositories.event_repository import EventRepository
from app.repositories.event_type_repository import EventTypeRepository
from app.repositories.workflow_repository import WorkFlowRepository
from app.schemas.event_type import EventCreate, EventStatus, Event, ValidationResponse, ValidationError as SchemaValidationError
from app.services.validation_service import ValidationService
from app.services.temporal_service import TemporalService
from app.services.workflow_service import WorkFlowService
from app.utils.exceptions import NotFoundError, ValidationError, DatabaseError
from app.utils.logging import get_logger

logger = get_logger("services.event")


class EventService:
    """Service for event processing operations."""
    
    def __init__(
        self,
        mongodb: AsyncIOMotorDatabase,
        workflow_service: Optional[WorkFlowService] = None,
        temporal_service: Optional[TemporalService] = None
    ):
        """
        Initialize event service.
        
        Args:
            mongodb: MongoDB database instance
            workflow_service: Optional workflow service instance
            temporal_service: Optional temporal service instance
        """
        self.event_repository = EventRepository(mongodb)
        self.event_type_repository = EventTypeRepository(mongodb)
        self.validation_service = ValidationService()
        self.workflow_service = workflow_service
        self.temporal_service = temporal_service
        
        # Ensure indexes are created
        self._ensure_indexes_task = None
    
    async def ensure_indexes(self) -> None:
        """Ensure database indexes are created."""
        if self._ensure_indexes_task is None:
            self._ensure_indexes_task = True
            await self.event_repository.ensure_indexes()
    
    async def add_event(self, event_data: EventCreate) -> Dict[str, Any]:
        """
        Add an event to the system.
        
        Args:
            event_data: Event creation data
            
        Returns:
            Dict[str, Any]: Created event
        """
       
        try:
            created_event = await self.create_event(event_data)
            event_id = created_event["id"]
            logger.info(f"Event stored with ID: {event_id}, data: {created_event}")
            
            validation_result = await self.validate_event_data(event_id)
            status = validation_result.valid and EventStatus.VALID or EventStatus.VALIDATION_FAILED
            errors = [error.message for error in validation_result.errors] if not validation_result.valid else None
            updated_event = await self.update_event_status(
                        event_id, 
                        status,
                        validation_errors=errors
                    )

            logger.info(f"Event {event_id} validation successful")
            if updated_event is None:
                raise NotFoundError(f"Event with ID {event_id} not found after validation")

            return updated_event
        except Exception as e:
            logger.error(f"Error adding event: {e}")
            raise

        

    async def create_event(self, event_data: EventCreate) -> Dict[str, Any]:
        """
        Create a new event with initial status.
        
        Args:
            event_data: Event creation data
            
        Returns:
            Dict[str, Any]: Created event
            
        Raises:
            DatabaseError: If database operation fails
        """
        logger.info(f"Creating event of type: {event_data.event_type}")
        
        # Ensure indexes exist
        await self.ensure_indexes()
        
        # Create the event
        created_event = await self.event_repository.create(event_data)
        logger.info(f"Successfully created event: {created_event['id']}")
        return created_event
    
    async def validate_event_data(self, event_id: str) -> ValidationResponse:
        """
        Validate event data against its event type schema.
        
        Args:
            event_id: Event ID
            
        Returns:
            Dict[str, Any]: Updated event with validation results
            
        Raises:
            NotFoundError: If event or event type not found
            DatabaseError: If database operation fails
        """
        logger.info(f"Validating event data for event: {event_id}")
        
        # Get the event
        event = await self.event_repository.get_by_id(event_id)
        if not event:
            raise NotFoundError(f"Event with ID {event_id} not found")
        
        # Get the event type
        event_type = await self.event_type_repository.get_by_name(event["event_type"])
        if not event_type:
            raise NotFoundError(f"Event type '{event['event_type']}' not found")
        
        try:
            # Validate the event data against the schema
            validation_result = self.validation_service.validate_data_against_schema(
                event["data"], 
                event_type["json_schema"]
            )
            return validation_result

        except Exception as e:
            logger.error(f"Error during event validation: {e}")
            return ValidationResponse(valid=False, errors=[SchemaValidationError(message=f"Unexpected error: {str(e)}", field="data", invalid_value="")])

    async def update_event_status(self, event_id: str, status: EventStatus, validation_errors: Optional[List[str]] = None):
        """
        Update event status based on validation result.
        
        Args:
            event_id: Event ID
            validation_result: Validation result
            
        Returns:
            Dict[str, Any]: Updated event
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            logger.info(f"Updating event status for event: {event_id}, status: {status}")
            updated_event = await self.event_repository.update_event_details(event_id, status, validation_errors=validation_errors)
            return updated_event
            
        except Exception as e:
            logger.error(f"Error updating event status: {e}")
            raise
    
    async def update_event_running_workflow(self, event_id: str, execution_id: str, status: EventStatus) -> Optional[Dict[str, Any]]:
        logger.info(f"Updating event execution id and status for event: {event_id}, status: {status}, execution_id: {execution_id}")
        updated_event = await self.event_repository.update_event_details(event_id=event_id, status=status, execution_id=execution_id)
        return updated_event if updated_event is not None else {}

    async def get_event(self, event_id: str) -> Optional[Dict[str, Any]]:
        """
        Get event by ID.
        
        Args:
            event_id: Event ID
            
        Returns:
            Optional[Dict[str, Any]]: Event data or None
        """
        return await self.event_repository.get_by_id(event_id)
    
    async def get_events_by_status(
        self, 
        status: str, 
        limit: int = 100, 
        skip: int = 0
    ) -> List[Dict[str, Any]]:
        """
        Get events by status.
        
        Args:
            status: Event status
            limit: Maximum number of events to return
            skip: Number of events to skip
            
        Returns:
            List[Dict[str, Any]]: List of events
        """
        return await self.event_repository.get_by_status(status, limit, skip)
    
    async def get_events_by_correlation_id(self, correlation_id: str) -> List[Dict[str, Any]]:
        """
        Get events by correlation ID.
        
        Args:
            correlation_id: Correlation ID
            
        Returns:
            List[Dict[str, Any]]: List of events
        """
        return await self.event_repository.get_by_correlation_id(correlation_id)
