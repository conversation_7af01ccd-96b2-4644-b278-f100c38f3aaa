"""
Schema validation service for dynamic schema and data validation.
"""

import re
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from email_validator import validate_email, EmailNotValidError

from app.schemas.schema import SchemaField, FieldType
from app.schemas.dynamic_record import DynamicRecordValidationError, DynamicRecordValidationResponse
from app.utils.logging import get_logger
from app.utils.constant import AUDIT_FIELD_NAMES

logger = get_logger("services.schema_validation")



class SchemaValidationService:
    """Service for validating schemas and data against schemas."""
    
    @staticmethod
    def validate_schema_definition(fields: List[SchemaField]) -> List[str]:
        """
        Validate schema field definitions.
        
        Args:
            fields: List of schema fields
            
        Returns:
            List[str]: List of validation errors (empty if valid)
        """
        errors = []
        field_names = set()
        
        for i, field in enumerate(fields):
            # Check for duplicate field names
            if field.name in field_names:
                errors.append(f"Duplicate field name '{field.name}' at position {i}")
            field_names.add(field.name)
            
            # Validate field name
            if not re.match(r'^[a-z][a-z0-9_]*$', field.name) and field.name not in AUDIT_FIELD_NAMES:
                errors.append(f"Invalid field name '{field.name}' - must start with letter and contain only lowercase letters, numbers, and underscores")
            
            # Validate field type specific rules
            validation_errors = SchemaValidationService._validate_field_rules(field)
            errors.extend(validation_errors)
        
        # Check for at least one required field
        if not any(field.validation_rules.required for field in fields):
            errors.append("At least one field must be required")
        
        return errors
    
    @staticmethod
    def _validate_field_rules(field: SchemaField) -> List[str]:
        """
        Validate individual field rules.
        
        Args:
            field: Schema field to validate
            
        Returns:
            List[str]: List of validation errors
        """
        errors = []
        rules = field.validation_rules
        
        # String field validations
        if field.field_type == FieldType.STRING:
            if rules.min_length is not None and rules.min_length < 0:
                errors.append(f"Field '{field.name}': min_length cannot be negative")
            if rules.max_length is not None and rules.max_length < 0:
                errors.append(f"Field '{field.name}': max_length cannot be negative")
            if (rules.min_length is not None and rules.max_length is not None and 
                rules.min_length > rules.max_length):
                errors.append(f"Field '{field.name}': min_length cannot be greater than max_length")
            
            # Validate regex pattern if provided
            if rules.pattern:
                try:
                    re.compile(rules.pattern)
                except re.error as e:
                    errors.append(f"Field '{field.name}': invalid regex pattern - {str(e)}")
        
        # Numeric field validations
        elif field.field_type in [FieldType.NUMBER, FieldType.INTEGER]:
            if (rules.min_value is not None and rules.max_value is not None and 
                rules.min_value > rules.max_value):
                errors.append(f"Field '{field.name}': min_value cannot be greater than max_value")
            
            # For integer fields, ensure min/max values are integers
            if field.field_type == FieldType.INTEGER:
                if rules.min_value is not None and not isinstance(rules.min_value, int):
                    errors.append(f"Field '{field.name}': min_value must be an integer for integer fields")
                if rules.max_value is not None and not isinstance(rules.max_value, int):
                    errors.append(f"Field '{field.name}': max_value must be an integer for integer fields")
        
        # Email and phone fields should not have length restrictions
        elif field.field_type in [FieldType.EMAIL, FieldType.PHONE]:
            if rules.min_length is not None or rules.max_length is not None:
                errors.append(f"Field '{field.name}': {field.field_type.value} fields should not have length restrictions")
        
        # Validate enum values
        if rules.enum_values:
            if len(rules.enum_values) != len(set(rules.enum_values)):
                errors.append(f"Field '{field.name}': enum values must be unique")
        
        return errors
    
    @staticmethod
    def validate_data_against_schema(data: Dict[str, Any], schema_fields: List[SchemaField]) -> DynamicRecordValidationResponse:
        """
        Validate data against schema fields.
        
        Args:
            data: Data to validate
            schema_fields: Schema field definitions
            
        Returns:
            DynamicRecordValidationResponse: Validation result
        """
        errors = []
        
        # Create field lookup for easy access
        field_lookup = {field.name: field for field in schema_fields}
        
        # Check required fields
        for field in schema_fields:
            if field.validation_rules.required and field.name not in data:
                errors.append(DynamicRecordValidationError(
                    field=field.name,
                    message=f"Required field '{field.display_name}' is missing",
                    invalid_value=None
                ))
        
        # Validate provided fields
        for field_name, value in data.items():
            if field_name not in field_lookup:
                errors.append(DynamicRecordValidationError(
                    field=field_name,
                    message=f"Unknown field '{field_name}'",
                    invalid_value=value
                ))
                continue
            
            field = field_lookup[field_name]
            field_errors = SchemaValidationService._validate_field_value(field, value)
            errors.extend(field_errors)
        
        return DynamicRecordValidationResponse(
            valid=len(errors) == 0,
            errors=errors
        )
    
    @staticmethod
    def _validate_field_value(field: SchemaField, value: Any) -> List[DynamicRecordValidationError]:
        """
        Validate a single field value.
        
        Args:
            field: Schema field definition
            value: Value to validate
            
        Returns:
            List[DynamicRecordValidationError]: List of validation errors
        """
        errors = []
        rules = field.validation_rules
        
        # Handle null/None values
        if value is None:
            if rules.required:
                errors.append(DynamicRecordValidationError(
                    field=field.name,
                    message=f"Field '{field.display_name}' is required",
                    invalid_value=value
                ))
            return errors
        
        # Type validation
        type_error = SchemaValidationService._validate_field_type(field, value)
        if type_error:
            errors.append(type_error)
            return errors  # Don't continue with other validations if type is wrong
        
        # Field-specific validations
        if field.field_type == FieldType.STRING:
            errors.extend(SchemaValidationService._validate_string_field(field, value))
        elif field.field_type in [FieldType.NUMBER, FieldType.INTEGER]:
            errors.extend(SchemaValidationService._validate_numeric_field(field, value))
        elif field.field_type == FieldType.EMAIL:
            errors.extend(SchemaValidationService._validate_email_field(field, value))
        elif field.field_type == FieldType.PHONE:
            errors.extend(SchemaValidationService._validate_phone_field(field, value))
        elif field.field_type == FieldType.URL:
            errors.extend(SchemaValidationService._validate_url_field(field, value))
        elif field.field_type in [FieldType.DATE, FieldType.DATETIME]:
            errors.extend(SchemaValidationService._validate_date_field(field, value))
        
        # Enum validation
        if rules.enum_values and value not in rules.enum_values:
            errors.append(DynamicRecordValidationError(
                field=field.name,
                message=f"Value must be one of: {', '.join(map(str, rules.enum_values))}",
                invalid_value=value
            ))
        
        return errors
    
    @staticmethod
    def _validate_field_type(field: SchemaField, value: Any) -> Optional[DynamicRecordValidationError]:
        """Validate field type."""
        expected_types = {
            FieldType.STRING: str,
            FieldType.NUMBER: (int, float),
            FieldType.INTEGER: int,
            FieldType.BOOLEAN: bool,
            FieldType.EMAIL: str,
            FieldType.PHONE: str,
            FieldType.URL: str,
            FieldType.DATE: str,
            FieldType.DATETIME: str,
            FieldType.OBJECT: dict,
            FieldType.OBJECTID: dict,
            FieldType.ARRAY: list,
        }
        
        expected_type = expected_types.get(field.field_type)
        if expected_type and not isinstance(value, expected_type):
            return DynamicRecordValidationError(
                field=field.name,
                message=f"Expected {field.field_type.value}, got {type(value).__name__}",
                invalid_value=value
            )
        return None
    
    @staticmethod
    def _validate_string_field(field: SchemaField, value: str) -> List[DynamicRecordValidationError]:
        """Validate string field."""
        errors = []
        rules = field.validation_rules
        
        # Length validations
        if rules.min_length is not None and len(value) < rules.min_length:
            errors.append(DynamicRecordValidationError(
                field=field.name,
                message=f"Must be at least {rules.min_length} characters long",
                invalid_value=value
            ))
        
        if rules.max_length is not None and len(value) > rules.max_length:
            errors.append(DynamicRecordValidationError(
                field=field.name,
                message=f"Must be at most {rules.max_length} characters long",
                invalid_value=value
            ))
        
        # Pattern validation
        if rules.pattern and not re.match(rules.pattern, value):
            errors.append(DynamicRecordValidationError(
                field=field.name,
                message=f"Does not match required pattern",
                invalid_value=value
            ))
        
        return errors
    
    @staticmethod
    def _validate_numeric_field(field: SchemaField, value: Union[int, float]) -> List[DynamicRecordValidationError]:
        """Validate numeric field."""
        errors = []
        rules = field.validation_rules
        
        # Range validations
        if rules.min_value is not None and value < rules.min_value:
            errors.append(DynamicRecordValidationError(
                field=field.name,
                message=f"Must be at least {rules.min_value}",
                invalid_value=value
            ))
        
        if rules.max_value is not None and value > rules.max_value:
            errors.append(DynamicRecordValidationError(
                field=field.name,
                message=f"Must be at most {rules.max_value}",
                invalid_value=value
            ))
        
        return errors
    
    @staticmethod
    def _validate_email_field(field: SchemaField, value: str) -> List[DynamicRecordValidationError]:
        """Validate email field."""
        errors = []
        
        try:
            validate_email(value)
        except EmailNotValidError:
            errors.append(DynamicRecordValidationError(
                field=field.name,
                message="Invalid email format",
                invalid_value=value
            ))
        
        return errors
    
    @staticmethod
    def _validate_phone_field(field: SchemaField, value: str) -> List[DynamicRecordValidationError]:
        """Validate phone field."""
        errors = []
        
        # Remove all non-digit characters for validation
        digits_only = re.sub(r'\D', '', value)
        
        if len(digits_only) < 10:
            errors.append(DynamicRecordValidationError(
                field=field.name,
                message="Phone number must contain at least 10 digits",
                invalid_value=value
            ))
        
        return errors
    
    @staticmethod
    def _validate_url_field(field: SchemaField, value: str) -> List[DynamicRecordValidationError]:
        """Validate URL field."""
        errors = []
        
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        if not url_pattern.match(value):
            errors.append(DynamicRecordValidationError(
                field=field.name,
                message="Invalid URL format",
                invalid_value=value
            ))
        
        return errors
    
    @staticmethod
    def _validate_date_field(field: SchemaField, value: str) -> List[DynamicRecordValidationError]:
        """Validate date/datetime field."""
        errors = []
        
        try:
            if field.field_type == FieldType.DATE:
                # Expect YYYY-MM-DD format
                datetime.strptime(value, '%Y-%m-%d')
            elif field.field_type == FieldType.DATETIME:
                # Expect ISO format
                datetime.fromisoformat(value.replace('Z', '+00:00'))
        except ValueError:
            format_msg = "YYYY-MM-DD" if field.field_type == FieldType.DATE else "ISO datetime format"
            errors.append(DynamicRecordValidationError(
                field=field.name,
                message=f"Invalid date format, expected {format_msg}",
                invalid_value=value
            ))
        
        return errors
    
    @staticmethod
    def apply_default_values(data: Dict[str, Any], schema_fields: List[SchemaField]) -> Dict[str, Any]:
        """
        Apply default values to data where fields are missing.
        
        Args:
            data: Input data
            schema_fields: Schema field definitions
            
        Returns:
            Dict[str, Any]: Data with default values applied
        """
        result = data.copy()
        
        for field in schema_fields:
            if field.name not in result and field.default_value is not None:
                result[field.name] = field.default_value
        return result
    
    @staticmethod
    def sanitize_data(data: Dict[str, Any], schema_fields: List[SchemaField]) -> Dict[str, Any]:
        """
        Sanitize data by removing unknown fields and applying transformations.
        
        Args:
            data: Input data
            schema_fields: Schema field definitions
            
        Returns:
            Dict[str, Any]: Sanitized data
        """
        field_names = {field.name for field in schema_fields}
        # Remove unknown fields
        sanitized = {k: v for k, v in data.items() if k in field_names}
        # Apply field-specific sanitization
        for field in schema_fields:
            if field.name in sanitized:
                value = sanitized[field.name]
                
                # Trim strings
                if field.field_type == FieldType.STRING and isinstance(value, str):
                    sanitized[field.name] = value.strip()
                
                # Normalize email
                elif field.field_type == FieldType.EMAIL and isinstance(value, str):
                    sanitized[field.name] = value.lower().strip()
        return sanitized
    

