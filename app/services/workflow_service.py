"""
Service for WorkFlow business logic operations.
"""

from datetime import datetime
import uuid
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.workflow import WorkFlow, WorkFlowEntryPoint, WorkFlowVersion
from app.repositories.entry_point_repository import EntryPointRepository
from app.repositories.workflow_execution_repository import WorkflowExecutionRepository
from app.repositories.workflow_repository import WorkFlowRepository, WorkFlowVersionRepository
from app.repositories.tag_repository import TagRepository
from app.services.base_service import BaseService
from app.schemas.workflow import (
    WorkFlowCreate,
    WorkFlowEntryStartData,
    WorkFlowUpdate,
    WorkFlowList,
    WorkFlowFlattenedResponse,
    SimplifiedTag,
    WorkflowExecuteRequest,
    WorkflowExecutionCreate
)
from app.node.node_base.node_models import NodeStatus, WorkflowExecutionStatus, WorkflowModel, NodeData, NodeConnection, WorkflowStatus
from app.utils.exceptions import NotFoundError, ValidationError
from app.utils.logging import get_logger
from app.node.node_utils.registry import node_registry
from app.services.temporal_service import TemporalService

logger = get_logger("services.workflow")


class WorkFlowService(BaseService[WorkFlow, WorkFlowCreate, WorkFlowUpdate, WorkFlowRepository]):
    """Service for WorkFlow business logic operations."""
    
    def __init__(self, db: AsyncSession):
        """
        Initialize service with database session.
        
        Args:
            db: Database session
        """
        self.db = db
        self.workflow_repository = WorkFlowRepository(db)
        self.version_repository = WorkFlowVersionRepository(db)
        self.tag_repository = TagRepository(db)
        self.execution_repository = WorkflowExecutionRepository(db)
        self.entry_point_repository = EntryPointRepository(db)
        super().__init__(self.workflow_repository)

    async def create_empty_workflow(self, user_id: int) -> Optional[WorkFlow]:
        """Create an empty workflow with no initial version.
        Args:
            user_id: ID of the user creating the workflow
        Returns:
            WorkFlow: Created empty workflow
        """
        try:
            # Create empty workflow
            workflow_name = f"My Workflow {await self.repository.count() + 1}"
            workflow = WorkFlow(
                name=workflow_name,
                is_active=False,
                created_by=user_id, 
                edited_by=user_id,
            )
            self.db.add(workflow)
            await self.db.flush()  # Get the UID
            await self.db.refresh(workflow)  # Ensure the UID is properly loaded

            workflow_version = WorkFlowVersion(
                version_no=1,
                work_flow={},
                workflow_id=workflow.id,
                status=WorkflowStatus.DRAFT,
                created_by=user_id,
                edited_by=user_id
            )
            self.db.add(workflow_version)
            await self.db.flush()

            # Set active version
            workflow.current_version_id = workflow_version.id
            await self.db.commit()
            await self.db.refresh(workflow)

            workflow_with_version = await self.workflow_repository.get_with_version(workflow.id)

            return workflow_with_version

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to create empty workflow: {str(e)}")
            raise
    
    async def get_workflow(self, workflow_id: uuid.UUID) -> WorkFlow:
        """
        Get workflow with all its versions and relationships.
        
        Args:
            workflow_id: Workflow UID
            
        Returns:
            WorkFlow: Workflow with versions and tags
            
        Raises:
            NotFoundError: If workflow not found
        """
        workflow = await self.workflow_repository.get_with_version(workflow_id)
        if not workflow:
            raise NotFoundError(f"Workflow with id {workflow_id} not found")
        
        return workflow
    
    async def update_workflow(
        self,
        workflow_id: uuid.UUID,
        workflow_update: WorkFlowUpdate,
        user_id: int,
    ) -> Optional[WorkFlow]:
        """
        Update workflow and create a new version if workflow logic changes.
        
        Args:
            workflow_id: Workflow UID
            workflow_update: Workflow update data
            new_version_data: New version workflow definition
            version_tag_id: Optional tag ID for the new version
            
        Returns:
            WorkFlow: Updated workflow
            
        Raises:
            NotFoundError: If workflow not found
            ValidationError: If update data is invalid
        """
        try:
            # Get existing workflow
            workflow = await self.workflow_repository.get_with_version(workflow_id)
            if not workflow:
                raise NotFoundError(f"Workflow with id {workflow_id} not found")
            
            # Update workflow metadata
            update_dict = workflow_update.model_dump(exclude_unset=True, exclude={"tag_ids", "work_flow","version_name"})
            for field, value in update_dict.items():
                setattr(workflow, field, value)

            if workflow_update.work_flow:
                current_version = workflow.current_version
                
                if current_version and  current_version.status == WorkflowStatus.DRAFT:
                    current_version.work_flow = workflow_update.work_flow.model_dump()
                    current_version.edited_by = user_id
                    if workflow_update.version_name:
                        current_version.version_name = workflow_update.version_name
                    await self.db.flush()
                    await self.db.refresh(current_version)

                else:
                    # Create a new version if the current version is not draft
                    new_version_no = (current_version.version_no + 1) if current_version else 1
                    new_version = WorkFlowVersion(
                        version_no=new_version_no,
                        work_flow=workflow_update.work_flow.model_dump(),
                        workflow_id=workflow.id,
                        status=WorkflowStatus.DRAFT,
                        version_name=workflow_update.version_name,
                        created_by=user_id,
                        edited_by=user_id
                    )
                    self.db.add(new_version)
                    await self.db.flush()
                    
                    # Set the new version as the active version
                    workflow.current_version_id = new_version.id
            
            # Handle tag updates
            if hasattr(workflow_update, 'tag_ids') and workflow_update.tag_ids is not None:
                await self._update_workflow_tags(workflow, workflow_update.tag_ids)
            
            await self.db.commit()
            await self.db.refresh(workflow)
            
            # Reload with relationships
            updated_workflow = await self.workflow_repository.get_with_version(workflow_id)
            
            logger.info(
                "Workflow updated",
                workflow_id=workflow_id,
                new_version_created=workflow_update.work_flow is not None,
                # updated_by=workflow_update.edited_by if hasattr(workflow_update, 'edited_by') else None
            )
            
            return updated_workflow

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to update workflow {workflow_id}: {str(e)}")
            raise
    
    async def get_workflows_paginated(
        self,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        active_only: bool = False
    ) -> WorkFlowList:
        """
        Get paginated list of workflows.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            filters: Additional filters
            active_only: Whether to return only active workflows
            
        Returns:
            WorkFlowList: Paginated workflow list
        """
        new_filters = filters.copy() if filters else {}
        if active_only:
            new_filters["is_active"] = True
        workflows = await self.workflow_repository.get_workflows(skip, limit, new_filters)
        total = await self.workflow_repository.count_workflows(new_filters)
        
        page = (skip // limit) + 1 if limit > 0 else 1

        # Process workflows one by one instead of using a list comprehension
        flattened_workflows = []
        for workflow in workflows:
            try:
                # Ensure each workflow has required relationships loaded
                flattened_response = self.to_flattened_response(workflow)
                flattened_workflows.append(flattened_response)
            except Exception as e:
                # Log error and continue with other workflows
                logger.error(f"Error processing workflow {workflow.id}: {str(e)}")


        return WorkFlowList(
            workflows=flattened_workflows,
            total=total,
            page=page,
            size=limit
        )
    
    async def _validate_tag_ids(self, tag_ids: List[int]) -> None:
        """
        Validate that all provided tag IDs exist.

        Args:
            tag_ids: List of tag IDs to validate

        Raises:
            ValidationError: If any tag ID is invalid
        """
        for tag_id in tag_ids:
            tag_exists = await self.tag_repository.exists(tag_id)
            if not tag_exists:
                raise ValidationError(f"Tag with id {tag_id} does not exist")

    def _validate_node_types(self, nodes: Dict[str, Dict[str, Any]]) -> None:
        """
        Validate that all node types in the workflow are registered and valid.

        Args:
            nodes: Dictionary of nodes with node IDs as keys and node data as values

        Raises:
            ValidationError: If any node type is invalid or not registered
        """
        if not nodes:
            return

        for node_id, node_data in nodes.items():
            if not isinstance(node_data, dict):
                raise ValidationError(f"Node '{node_id}' data must be a dictionary")

            node_type = node_data.get('type')
            if not node_type:
                raise ValidationError(f"Node '{node_id}' is missing required 'type' field")

            if not isinstance(node_type, str):
                raise ValidationError(f"Node '{node_id}' type must be a string, got {type(node_type).__name__}")

            # Get node class from registry
            node_class = node_registry.get_node_class(node_type)
            if not node_class:
                raise ValidationError(f"Node type '{node_type}' is not registered in the system")
            
            # Validate the node type by calling the validate method
            try:
                # Create a minimal NodeRequest for validation
                from app.node.node_base.node_models import NodeRequest
                node_request = NodeRequest(
                    type=node_type,
                    parameters=node_data.get('parameters', {}),
                    display_properties=node_data.get('position', {}),
                    name=node_id,
                    is_trigger=node_data.get('is_trigger', False),
                    credentials= node_data.get('credentials', {})
                )
                # Create node instance and validate
                node_instance = node_class()
                if node_request.is_trigger and (node_instance.description.inputs and len(node_instance.description.inputs) != 0):
                    raise ValidationError(f"Node '{node_id}' of type '{node_type}' is not a valid trigger node")

                if not node_request.is_trigger and (node_instance.description.inputs and len(node_instance.description.inputs) == 0):
                    raise ValidationError(f"Node '{node_id}' of type '{node_type}' requires inputs but none are provided")
                
                validation_result = node_instance.validate(node_request)
                if not validation_result.valid:
                    error_messages = []
                    if validation_result.errors:
                        error_messages = [f"{error.parameter}: {error.message}" for error in validation_result.errors]
                    raise ValidationError(f"Node '{node_id}' of type '{node_type}' validation failed: {'; '.join(error_messages)}")

            except Exception as e:
                if isinstance(e, ValidationError):
                    raise
                raise ValidationError(f"Node type '{node_type}' validation failed for node '{node_id}': {str(e)}")
    
    async def _update_workflow_tags(self, workflow: WorkFlow, new_tag_ids: List[int]) -> None:
        """
        Update workflow tags by replacing current tags with new ones.
        
        Args:
            workflow: Workflow instance
            new_tag_ids: New list of tag IDs
        """
        # Validate new tag IDs
        if new_tag_ids:
            await self._validate_tag_ids(new_tag_ids)
        
        # Get current tag IDs
        current_tag_ids = [tag.id for tag in workflow.tags]
        
        # Remove tags that are no longer needed
        tags_to_remove = [tag_id for tag_id in current_tag_ids if tag_id not in new_tag_ids]
        if tags_to_remove:
            await self.workflow_repository.remove_tags(workflow, tags_to_remove)
        
        # Add new tags
        tags_to_add = [tag_id for tag_id in new_tag_ids if tag_id not in current_tag_ids]
        if tags_to_add:
            await self.workflow_repository.add_tags(workflow, tags_to_add)

    async def create_workflow_entry_point(self, workflow : WorkFlow):
        """
        Create a new workflow entry point.
        
        Args:
            workflow_id: Workflow UID
            
        Returns:
            WorkFlowEntryPoint: Created entry point
        """
        if not workflow.published_version_id:
            raise ValidationError("Workflow must have an published version with workflow definition")
        
        published_version = await self.version_repository.get(workflow.published_version_id)

        if not published_version:
            raise ValidationError("Workflow must have an published version with workflow definition")

        work_flow = published_version.work_flow
        if not work_flow.get('nodes'):
            raise ValidationError("Workflow definition must contain nodes")

        # Convert nodes to dictionary format
        nodes_dict = {}
        for node_id, node in work_flow['nodes'].items():
            try:
                node_data = node.model_dump() if hasattr(node, "model_dump") else dict(node)
                nodes_dict[node_id] = node_data
            except Exception as e:
                logger.error(f"Failed to convert node {node_id}: {str(e)}")
                raise ValidationError(f"Invalid node data for node {node_id}")

        # Validate node types
        self._validate_node_types(nodes_dict)

        # Find trigger nodes
        start_nodes = [
            NodeData(**node)
            for _, node in nodes_dict.items() 
            if node and isinstance(node, dict) and node.get('is_trigger', False) and node.get('type') != "manual_trigger"
        ]

        if not start_nodes:
            logger.warning("No trigger nodes found in workflow")
            raise ValidationError("No trigger nodes found in workflow")
        entry_points = []
        for node in start_nodes:
            # trigger_value = ",".join([str(v) for _, v in node.parameters.items()]),
            node_class = node_registry.get(node.type)
            if node_class is None or not hasattr(node_class, "create_trigger_value"):
                logger.error(f"Node type '{node.type}' is not registered or missing 'create_trigger_value' method")
                continue
            trigger_value = node_class.create_trigger_value(node)
            if not trigger_value:
                raise ValidationError(f"Trigger value for node type '{node.type}' is empty")
            
            if not node_class.allow_multiple_trigger_values:
                existing_entry_point = await self.entry_point_repository.find_by_trigger(node.type, trigger_value)
                workflow_ids = list({entry_point.workflow_id for entry_point in entry_points})
                if existing_entry_point and not workflow.id not in workflow_ids :
                    raise ValidationError(f"Value already exists.!")
            
            entry_points.append(WorkFlowEntryPoint(
            workflow_id=workflow.id,
            trigger_type=node.type,
            trigger_value=trigger_value,
            is_active=True,
            start_nodes=[node.name],
            attributes={},
        ))
        
            
        self.db.add_all(entry_points)
        await self.db.flush()
        

    async def publish_workflow_version(self, workflow_id: uuid.UUID, user_id: int) -> WorkFlow:
        """
        Publish a workflow version.
        
        Args:
            workflow_id: Workflow UID
            version_id: Version ID to publish
            user_id: ID of the user publishing the version
            
        Returns:
            WorkFlow: Updated workflow with published version
            
        Raises:
            NotFoundError: If workflow or version not found
            ValidationError: If version is not in draft state
        """
        try:
            workflow = await self.workflow_repository.get_with_version(workflow_id)
            if not workflow:
                raise NotFoundError(f"Workflow with id {workflow_id} not found")

            if not workflow.current_version:
                raise NotFoundError(f"No active version found for workflow {workflow_id}")

            current_version = workflow.current_version
            
            if current_version.status != WorkflowStatus.DRAFT:
                raise ValidationError(f"Cannot publish version {current_version.id} - it is not in DRAFT state")

            # Set as published
            current_version.status = WorkflowStatus.PUBLISHED
            current_version.edited_by = user_id

            # Validate node types in the workflow
            self._validate_node_types(current_version.work_flow.get('nodes', {}))

            await self.db.flush()
            await self.db.refresh(current_version)

            # Update published version ID in workflow
            workflow.published_version_id = current_version.id
            
            await self.db.flush()
            await self.db.refresh(workflow)

            await self.create_workflow_entry_point(workflow)

            await self.db.commit()

            logger.info(f"Published workflow version {current_version.id} for workflow {workflow_id}")

            return workflow
        
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to publish workflow version: {str(e)}")
            raise

    def to_flattened_response(self, workflow: WorkFlow) -> WorkFlowFlattenedResponse:
        """
        Convert a WorkFlow object to a flattened response format.

        This method combines workflow metadata with active version data into a single
        flattened structure, making it easier for frontend consumption.

        Args:
            workflow: WorkFlow object with loaded active_version and tags relationships

        Returns:
            WorkFlowFlattenedResponse: Flattened response with combined data

        Raises:
            ValidationError: If workflow doesn't have an active version
        """
        if not workflow.current_version:
            raise ValidationError("Workflow must have a current version for flattened response")

        # Convert tags to simplified format
        simplified_tags = [
            SimplifiedTag(id=tag.id, name=tag.name)
            for tag in workflow.tags
        ]

        # Create flattened response
        return WorkFlowFlattenedResponse(
            # Workflow metadata fields
            created_at=workflow.created_at.isoformat() + "Z",
            updated_at=workflow.updated_at.isoformat() + "Z",
            name=workflow.name,
            is_active=workflow.is_active,
            description=workflow.description,
            status=workflow.current_version.status,
            created_by= workflow.creator.full_name if workflow.creator else 'Unknown',
            edited_by=workflow.editor.full_name if workflow.editor else None,
            id=str(workflow.id),

            # Simplified tags
            tags=simplified_tags,

            # Active version fields (flattened)
            published_version_id=workflow.published_version_id,
            current_version_id=workflow.current_version.id,
            version_name=workflow.current_version.version_name,
            work_flow=workflow.current_version.work_flow
        )

    async def convert_to_workflow_model(self, workflow_data: WorkFlowCreate) -> WorkflowModel:
        """
        Convert a WorkFlowCreate schema to a WorkflowModel for execution.

        This method transforms the API schema format to the execution model format,
        converting nodes and connections to the structure expected by the workflow executor.

        Args:
            workflow_data: WorkFlowCreate schema with workflow definition

        Returns:
            WorkflowModel: Converted workflow model ready for execution

        Raises:
            ValidationError: If workflow definition is missing or invalid
        """
        from app.node.node_base.node_models import NodeData, NodeConnection

        if not workflow_data.work_flow:
            raise ValidationError("Workflow definition (work_flow) is required for test execution")

        work_flow_def = workflow_data.work_flow

        if not getattr(work_flow_def, 'start_node', None):
            raise ValidationError("Start node is required for workflow execution")

        if not work_flow_def.nodes:
            raise ValidationError("At least one node is required for workflow execution")

        # Convert nodes from Dict[str, Dict[str, Any]] to Dict[str, NodeData]
        converted_nodes = {}
        for node_id, node_data in work_flow_def.nodes.items():
            if not isinstance(node_data, dict):
                raise ValidationError(f"Node '{node_id}' data must be a dictionary")

            # Extract required fields with defaults
            node_type = node_data.get('type')
            if not node_type:
                raise ValidationError(f"Node '{node_id}' is missing required 'type' field")

            position = node_data.get('position', [0, 0])
            parameters = node_data.get('parameters', {})

            # Create NodeData instance
            converted_nodes[node_id] = NodeData(
                name=node_id,
                type=node_type,
                parameters=parameters,
                credentials=node_data.get('credentials', {})
            )

        # Convert connections from Dict[str, Dict[str, List[List[str]]]] to Dict[str, NodeConnection]
        converted_connections = {}
        if work_flow_def.connections:
            for node_id, connection_data in work_flow_def.connections.items():
                if isinstance(connection_data, dict) and 'main' in connection_data:
                    converted_connections[node_id] = NodeConnection(main=connection_data['main'])
                else:
                    # Handle legacy format or create empty connection
                    converted_connections[node_id] = NodeConnection(main=[])

        # Create WorkflowModel
        workflow_model = WorkflowModel(
            id=str(uuid.uuid4()),  # Generate unique ID for test execution
            name=workflow_data.name,
            display_name=workflow_data.name,
            description=workflow_data.description or "",
            is_active=workflow_data.is_active,
            status=workflow_data.status,
            version="test-1.0.0",
            initial_data={},  # initial_data is not available in WorkFlowCreate schema
            start_node=work_flow_def.start_node,
            nodes=converted_nodes,
            connections=converted_connections
        )

        return workflow_model

    async def execute_test_workflow(self, workflow_id: uuid.UUID, data: WorkflowExecuteRequest, user_id: int) -> str:
        """
        Execute a test workflow by ID.

        Args:
            workflow_id: Workflow UUID
            data: Workflow execution request data
            user_id: ID of the user executing the workflow

        Returns:
            None
        """
        if not data.work_flow or not hasattr(data.work_flow, "nodes") or data.work_flow.nodes is None:
            raise ValidationError("Workflow definition (work_flow) with nodes is required for test execution")

        # Convert NodeRequest objects to dicts for validation
        nodes_dict = {
            node_id: node.model_dump() if hasattr(node, "model_dump") else dict(node)
            for node_id, node in data.work_flow.nodes.items()
        }
        self._validate_node_types(nodes_dict)
        start_nodes = [node_id for node_id, node in nodes_dict.items() if node.get('is_trigger', False)]
        workflow_details = await self.workflow_repository.get_by_uid(workflow_id)
        if not workflow_details:
            raise NotFoundError(f"Workflow with id {workflow_id} not found")
        node_data = {key: NodeData(**value) for key, value in nodes_dict.items()}
        node_connections = {
            key: NodeConnection(**value) for key, value in data.work_flow.connections.items()
        } if data.work_flow.connections else {}
        workflow_model = WorkflowModel(
            id=str(workflow_id),
            name=workflow_details.name,
            display_name=workflow_details.name,
            description=workflow_details.description or "",
            is_active=workflow_details.is_active,
            initial_data=data.input_data or {},
            start_node=start_nodes,
            nodes=node_data,
            connections=node_connections
        )
        temporal_service = TemporalService()
        execution_id = uuid.uuid4()
        request = WorkflowExecutionCreate(
            id=execution_id,
            workflow_id=workflow_id,
            workflow_version_id=workflow_details.current_version_id if workflow_details.current_version_id else 0,
            result=workflow_model,
            is_test=True,
            error_message=None,
            status=WorkflowExecutionStatus.RUNNING,
            created_by=user_id,
            updated_by=user_id
        )
        return await temporal_service.start_workflow(request)

    async def start_workflow(self, data: WorkFlowEntryStartData):
        """
        Start workflow execution based on trigger type and value.
        
        Args:
            execution_id: Optional workflow execution create model
            trigger_type: Type of trigger
            trigger_value: Value associated with the trigger
            input_data: Optional dictionary of input data for the workflow
            
        Returns:
            str: Comma-separated list of execution IDs
        """
        try:
            if not data.trigger_type or not data.trigger_value:
                raise ValidationError("trigger_type and trigger_value are required")

            # Get entry point repository instance
            entry_point_repository = EntryPointRepository(self.db)

            if data.trigger_type == 'webhook':
                webhook_class = node_registry.get_node_class(data.trigger_type)
                if webhook_class is None:
                    raise ValidationError("Webhook node type not registered in the system")
                webhook_instance = webhook_class()
                trigger_value = webhook_instance.get_trigger_value(data)
            else:
                trigger_value = data.trigger_value
            # Find matching entry points
            if not trigger_value:
                raise ValidationError("trigger_value are required.")
            entry_points = await entry_point_repository.find_by_trigger(
                trigger_type=data.trigger_type,
                trigger_value=trigger_value
            )

            if not entry_points:
                raise NotFoundError(
                    f"No workflow entry points found for trigger_type={data.trigger_type} "
                    f"and trigger_value={data.trigger_value}"
                )

            workflow_ids = list({entry_point.workflow_id for entry_point in entry_points})
            workflows = await self.workflow_repository.get_active_workflows_by_uids(workflow_ids)
            if not workflow_ids or not workflows:
                raise NotFoundError(
                    f"No active workflows found for trigger_type={data.trigger_type} "
                    f"and trigger_value={data.trigger_value}"
                )

            # Start workflow execution for each matching entry point
            temporal_service = TemporalService()
            execution_ids = []
            for entry_point in entry_points:
                workflow_details = workflows.get(entry_point.workflow_id)
                
                if not workflow_details:
                    logger.warning(
                        f"Skipping workflow {entry_point.workflow_id} for trigger "
                        f"{data.trigger_type}:{data.trigger_value}"
                    )
                    continue

                if entry_point.start_nodes and isinstance(entry_point.start_nodes, list) and len(entry_point.start_nodes) > 0:
                    node_name = entry_point.start_nodes[0]
                else:
                    logger.error(f"Entry point {entry_point.id} has no valid start_nodes")
                    continue
                # Use the workflow definition from the current or published version
                workflow_def = None
                if hasattr(workflow_details, "published_version") and workflow_details.published_version and hasattr(workflow_details.published_version, "work_flow"):
                    workflow_def = workflow_details.published_version.work_flow
                else:
                    raise ValidationError("Workflow definition not found in current or published version")

                start_node = node_registry.get(workflow_def['nodes'][node_name]['type'])
                if start_node is None:
                    logger.error(f"Node class for type '{workflow_def['nodes'][node_name]['type']}' not found in registry")
                    continue
                
                node_data = workflow_def['nodes'][node_name]
                node_data['input_data'] = data.input_data
                nodedata = NodeData(**node_data)
                nodedata.start_time = datetime.now().isoformat()
                node_result = await start_node.run(nodedata)
                if node_result.result is None:
                    continue

                nodedata.end_time = datetime.now().isoformat()
                nodedata.status = NodeStatus.COMPLETED
                nodedata.result = node_result.result
                nodedata.execution_time = int((datetime.fromisoformat(nodedata.end_time) - datetime.fromisoformat(nodedata.start_time)).total_seconds())
                nodedata.input_data = None
                workflow_def['nodes'][node_name] = nodedata.model_dump()
                start_nodes = workflow_def['connections'][node_name]['main'][node_result.next_connection_index]
                

                workflow_model = WorkflowModel(
                    id=str(data.execution_id),
                    name=workflow_details.name,
                    display_name=workflow_details.name,
                    description=workflow_details.description or "",
                    is_active=workflow_details.is_active,
                    # initial_data=data.input_data or {},
                    start_node=start_nodes,
                    nodes=workflow_def['nodes'],
                    connections=workflow_def['connections']
        )

                # Create execution request
                execution_request = WorkflowExecutionCreate(
                    id=data.execution_id,
                    workflow_id=workflow_details.id,
                    workflow_version_id=workflow_details.current_version_id if workflow_details.current_version_id is not None else 0,
                    result=workflow_model,
                    is_test=False,
                    error_message=None,
                    status=WorkflowExecutionStatus.RUNNING,
                    created_by= None,
                    updated_by=None
                )

                # Start workflow execution
                exec_id = await temporal_service.start_workflow(execution_request)
                return exec_id
            #     execution_ids.append(exec_id)

            #     logger.info(
            #         "Started workflow execution",
            #         workflow_id=str(workflow.id),
            #         execution_id=exec_id,
            #         trigger_type=trigger_type,
            #         trigger_value=trigger_value
            #     )

            # return ",".join(execution_ids)

        except Exception as e:
            logger.error(
                f"Failed to start workflow execution: {str(e)}",
                trigger_type=data.trigger_type,
                trigger_value=data.trigger_value,
                exc_info=True
            )
            raise
    
    async def get_workflow_execution_status(self, execution_id: str) -> Dict[str, Any]:
        """
        Get the status of a workflow execution by its execution ID.
        
        Args:
            execution_id: The workflow execution ID from start_workflow_by_id
            
        Returns:
            Dict with workflow execution status and details
            
        Raises:
            NotFoundError: If execution not found
            RuntimeError: If service is unavailable
        """
        try:
            # Initialize Temporal service
            temporal_service = TemporalService()
            if not await temporal_service.connect():
                raise RuntimeError("Failed to connect to workflow execution service")
            
            try:
                # Get workflow execution status
                result = await temporal_service.get_workflow_result(execution_id)
                
                logger.info(
                    "Retrieved workflow execution status",
                    execution_id=execution_id,
                    status=result.get("status")
                )
                
                return {
                    "execution_id": execution_id,
                    "status": result.get("status", "UNKNOWN"),
                    "start_time": result.get("start_time"),
                    "end_time": result.get("end_time"),
                    "execution_time": result.get("execution_time"),
                    "error": result.get("error"),
                    "result": result
                }
                
            finally:
                # Ensure we disconnect from Temporal properly
                await temporal_service.disconnect()
                
        except Exception as e:
            logger.error(
                f"Failed to get workflow execution status: {str(e)}",
                execution_id=execution_id,
                error=str(e),
                exc_info=True
            )
            raise RuntimeError(f"Failed to get workflow execution status: {str(e)}")
    
    def _create_workflow_model(self, workflow: WorkFlow, input_data: Dict[str, Any]) -> WorkflowModel:
        """
        Convert a WorkFlow database model to a WorkflowModel for execution.
        
        Args:
            workflow: WorkFlow database model with active_version loaded
            input_data: Input data for the workflow execution
            
        Returns:
            WorkflowModel ready for execution
        """
        # Get workflow definition from active version
        work_flow_def = workflow.active_version.work_flow
        
        # Convert nodes from Dict[str, Dict[str, Any]] to Dict[str, NodeData]
        converted_nodes = {}
        for node_id, node_data in work_flow_def.get("nodes", {}).items():
            # Validate node type is registered
            if not node_registry.get_node_class(node_data.get("type")):
                raise ValidationError(f"Unknown node type: {node_data.get('type')}")
                
            # Create NodeData objects
            converted_nodes[node_id] = NodeData(
                name=node_id,
                type=node_data.get("type"),
                parameters=node_data.get("parameters", {}),
                credentials=node_data.get("credentials"),
            )
        
        # Convert connections
        converted_connections = {}
        if work_flow_def.get("connections"):
            for node_id, connection_data in work_flow_def.get("connections", {}).items():
                if connection_data and "main" in connection_data:
                    converted_connections[node_id] = NodeConnection(main=connection_data["main"])
        
        # Merge input data with initial data
        initial_data = {**work_flow_def.get("initial_data", {}), **input_data}
        
        # Create WorkflowModel
        workflow_model = WorkflowModel(
            id=str(workflow.id),
            name=workflow.name,
            display_name=workflow.name,
            description=workflow.description or "",
            is_active=workflow.is_active,
            # status=work_flow_def.get("status", "DRAFT"),
            version=f"{workflow.active_version.version_no}",
            initial_data=initial_data,
            start_node=work_flow_def.get("start_node"),
            nodes=converted_nodes,
            connections=converted_connections
        )
        
        return workflow_model
    