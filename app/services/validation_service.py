"""
JSON Schema validation service for event type data validation.
"""

from typing import Any, Dict, List, Sequence, Union, TYPE_CHECKING, cast
import jsonschema
from jsonschema import Draft7Validator, ValidationError as JsonSchemaValidationError

from app.schemas.event_type import ValidationError, ValidationResponse
from app.utils.exceptions import ValidationError as CerebroValidationError
from app.utils.logging import get_logger

if TYPE_CHECKING:
    from app.schemas.event_type import JSONSchema

logger = get_logger("services.validation")


class ValidationService:
    """Service for JSON schema validation with detailed error reporting."""
    
    @staticmethod
    def validate_schema_definition(schema: Union[Dict[str, Any], 'JSONSchema']) -> None:
        """
        Validate that a schema definition is a valid JSON schema.

        Args:
            schema: JSON schema definition to validate (dict or JSONSchema object)

        Raises:
            ValidationError: If schema definition is invalid
        """
        try:
            # Convert JSONSchema object to dict if needed
            if hasattr(schema, 'to_dict'):
                schema_dict = cast('JSONSchema', schema).to_dict()
            elif isinstance(schema, dict):
                schema_dict = schema
            else:
                raise ValueError("Schema must be a dict or JSONSchema object")

            # List of valid format values according to JSON Schema
            VALID_FORMATS = [
                "date-time", "date", "time", "email", "hostname", 
                "ipv4", "ipv6", "uri", "uri-reference", "uuid", 
                "regex", "json-pointer"
            ]
            
            # Modified check_formats to handle nested format values
            def check_formats(obj):
                if isinstance(obj, dict):
                    if "format" in obj and isinstance(obj["format"], str):
                        if obj["format"] not in VALID_FORMATS:
                            raise CerebroValidationError(
                                f"Invalid format value: '{obj['format']}'. Must be one of: {', '.join(VALID_FORMATS)}"
                            )
                
                    # Check all child properties
                    for key, value in obj.items():
                        if isinstance(value, (dict, list)):
                            check_formats(value)
                elif isinstance(obj, list):
                    for item in obj:
                        if isinstance(item, (dict, list)):
                            check_formats(item)

            # Check format in JSON schema properties
            if "properties" in schema_dict:
                check_formats(schema_dict["properties"])
            if "items" in schema_dict:
                check_formats(schema_dict["items"])

            # Validate using jsonschema
            Draft7Validator.check_schema(schema_dict)
            logger.debug("Schema definition validation passed")
            
        except jsonschema.SchemaError as e:
            logger.error(f"Invalid schema definition: {e}")
            raise CerebroValidationError(f"Invalid schema definition: {e.message}")
        except CerebroValidationError as e:
            logger.error(f"Invalid schema format: {e}")
            raise
    
    @staticmethod
    def validate_data_against_schema(
        data: Union[Dict[str, Any], List[Any], str, int, float, bool, None],
        schema: Union[Dict[str, Any], 'JSONSchema']
    ) -> ValidationResponse:
        """
        Validate data against a JSON schema.

        Args:
            data: Data to validate
            schema: JSON schema to validate against (dict or JSONSchema object)

        Returns:
            ValidationResponse: Validation result with errors if any
        """
        try:
            # First validate the schema itself
            ValidationService.validate_schema_definition(schema)

            # Convert JSONSchema object to dict if needed
            if hasattr(schema, 'to_dict'):
                schema_dict = cast('JSONSchema', schema).to_dict()
            elif isinstance(schema, dict):
                schema_dict = schema
            else:
                raise ValueError("Schema must be a dict or JSONSchema object")

            # Create validator
            validator = Draft7Validator(schema_dict)

            # Collect all validation errors
            errors = []
            for error in validator.iter_errors(data):
                validation_error = ValidationService._format_validation_error(error)
                errors.append(validation_error)

            # Return validation response
            is_valid = len(errors) == 0
            logger.debug(f"Data validation result: valid={is_valid}, errors_count={len(errors)}")

            return ValidationResponse(valid=is_valid, errors=errors)

        except CerebroValidationError:
            # Re-raise schema validation errors
            raise
        except Exception as e:
            logger.error(f"Unexpected error during validation: {e}")
            raise CerebroValidationError(f"Validation failed: {e}")
    
    @staticmethod
    def _format_validation_error(error: JsonSchemaValidationError) -> ValidationError:
        """
        Format a jsonschema ValidationError into our custom ValidationError format.
        
        Args:
            error: jsonschema ValidationError
            
        Returns:
            ValidationError: Formatted validation error
        """
        # Build field path
        field_path = ValidationService._build_field_path(error.absolute_path)
        
        # Get the invalid value
        invalid_value = error.instance
        
        # Format error message
        message = ValidationService._format_error_message(error)
        
        return ValidationError(
            field=field_path,
            message=message,
            invalid_value=invalid_value
        )
    
    @staticmethod
    def _build_field_path(path: Sequence[Union[str, int]]) -> str:
        """
        Build a human-readable field path from jsonschema path.
        
        Args:
            path: List of path components
            
        Returns:
            str: Formatted field path
        """
        if not path:
            return "root"
        
        path_parts = []
        for part in path:
            if isinstance(part, int):
                path_parts.append(f"[{part}]")
            else:
                if path_parts:
                    path_parts.append(f".{part}")
                else:
                    path_parts.append(str(part))
        
        return "".join(path_parts)
    
    @staticmethod
    def _format_error_message(error: JsonSchemaValidationError) -> str:
        """
        Format error message with more user-friendly descriptions.
        
        Args:
            error: jsonschema ValidationError
            
        Returns:
            str: Formatted error message
        """
        validator_name = error.validator
        
        # Custom messages for common validation errors
        if validator_name == "required":
            missing_property = error.validator_value
            return f"Required property '{missing_property}' is missing"
        
        elif validator_name == "type":
            expected_type = error.validator_value
            actual_type = type(error.instance).__name__
            return f"Expected type '{expected_type}', got '{actual_type}'"
        
        elif validator_name == "enum":
            allowed_values = error.validator_value
            if isinstance(allowed_values, (list, tuple)):
                return f"Value must be one of: {', '.join(map(str, allowed_values))}"
            else:
                return f"Value must be one of the allowed values"
        
        elif validator_name == "minimum":
            minimum = error.validator_value
            return f"Value must be greater than or equal to {minimum}"
        
        elif validator_name == "maximum":
            maximum = error.validator_value
            return f"Value must be less than or equal to {maximum}"
        
        elif validator_name == "minLength":
            min_length = error.validator_value
            return f"String must be at least {min_length} characters long"
        
        elif validator_name == "maxLength":
            max_length = error.validator_value
            return f"String must be at most {max_length} characters long"
        
        elif validator_name == "pattern":
            pattern = error.validator_value
            return f"String does not match required pattern: {pattern}"
        
        elif validator_name == "minItems":
            min_items = error.validator_value
            return f"Array must have at least {min_items} items"
        
        elif validator_name == "maxItems":
            max_items = error.validator_value
            return f"Array must have at most {max_items} items"
        
        elif validator_name == "uniqueItems":
            return "Array items must be unique"
        
        elif validator_name == "additionalProperties":
            return "Additional properties are not allowed"
        
        elif validator_name == "format":
            format_name = error.validator_value
            return f"String does not match required format: {format_name}"
        
        else:
            # Fallback to original message
            return error.message
    
    @staticmethod
    def get_schema_summary(schema: Union[Dict[str, Any], 'JSONSchema']) -> Dict[str, Any]:
        """
        Generate a summary of schema structure for documentation purposes.

        Args:
            schema: JSON schema definition (dict or JSONSchema object)

        Returns:
            Dict[str, Any]: Schema summary
        """
        try:
            ValidationService.validate_schema_definition(schema)

            # Convert JSONSchema object to dict if needed
            if hasattr(schema, 'to_dict'):
                schema_dict = cast('JSONSchema', schema).to_dict()
            elif isinstance(schema, dict):
                schema_dict = schema
            else:
                raise ValueError("Schema must be a dict or JSONSchema object")

            summary = {
                "type": schema_dict.get("type", "unknown"),
                "description": schema_dict.get("description", ""),
                "required_fields": [],
                "optional_fields": [],
                "field_types": {}
            }

            if schema_dict.get("type") == "object":
                properties = schema_dict.get("properties", {})
                required = schema_dict.get("required", [])

                for field_name, field_schema in properties.items():
                    field_type = field_schema.get("type", "unknown")
                    summary["field_types"][field_name] = field_type

                    if field_name in required:
                        summary["required_fields"].append(field_name)
                    else:
                        summary["optional_fields"].append(field_name)

            return summary

        except Exception as e:
            logger.error(f"Failed to generate schema summary: {e}")
            return {"error": f"Failed to generate summary: {e}"}
    
    @staticmethod
    def validate_and_get_detailed_report(
        data: Union[Dict[str, Any], List[Any], str, int, float, bool, None],
        schema: Union[Dict[str, Any], 'JSONSchema']
    ) -> Dict[str, Any]:
        """
        Validate data and return a detailed validation report.

        Args:
            data: Data to validate
            schema: JSON schema to validate against (dict or JSONSchema object)

        Returns:
            Dict[str, Any]: Detailed validation report
        """
        validation_result = ValidationService.validate_data_against_schema(data, schema)
        schema_summary = ValidationService.get_schema_summary(schema)

        return {
            "validation_result": validation_result.model_dump(),
            "schema_summary": schema_summary,
            "data_type": type(data).__name__,
            "data_size": len(str(data)) if data is not None else 0
        }
    
    # @staticmethod
    # def validate_schema_definition(self, schema: Dict[str, Any]) -> None:
    #     """
    #     Validate JSON Schema definition.
        
    #     Args:
    #         schema: JSON Schema to validate
            
    #     Raises:
    #         ValidationError: If schema is invalid
    #     """
    #     # List of valid format values according to JSON Schema
    #     VALID_FORMATS = [
    #         "date-time", "date", "time", "email", "hostname", 
    #         "ipv4", "ipv6", "uri", "uri-reference", "uuid", 
    #         "regex", "json-pointer"
    #     ]
        
    #     # Check format value if present in root schema
    #     if "format" in schema and schema["format"] not in VALID_FORMATS:
    #         raise ValidationError(
    #             f"Invalid format value: '{schema['format']}'. Must be one of: {', '.join(VALID_FORMATS)}"
    #         )
        
    #     # Recursively check format in nested properties
    #     def check_formats(obj):
    #         if isinstance(obj, dict):
    #             if "format" in obj and obj["format"] not in VALID_FORMATS:
    #                 raise ValidationError(
    #                     f"Invalid format value: '{obj['format']}'. Must be one of: {', '.join(VALID_FORMATS)}"
    #                 )
                
    #             # Check all child properties
    #             for key, value in obj.items():
    #                 if isinstance(value, (dict, list)):
    #                     check_formats(value)
    #         elif isinstance(obj, list):
    #             for item in obj:
    #                 if isinstance(item, (dict, list)):
    #                     check_formats(item)
        
    #     # Check all nested properties and items
    #     if "properties" in schema:
    #         check_formats(schema["properties"])
    #     if "items" in schema:
    #         check_formats(schema["items"])
        
    #     try:
    #         # Use jsonschema to validate the basic structure
    #         jsonschema.validators.validate(schema, {})
    #     except jsonschema.exceptions.ValidationError as e:
    #         raise ValidationError(f"Invalid schema: {str(e)}")
