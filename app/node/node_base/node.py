"""
Nodfrom abc import ABC, abstractmethod
from typing import List, Optional, Any, ClassVar, Type

from .node_models import NodeTypeDescription, NodeData, NodeParameterdule

This module defines the abstract base class for all nodes in the application.
Nodes are the primary building blocks of workflows and represent individual processing units.
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Optional

from .node_models import (
    NodeTypeDescription, 
    NodeData, 
    NodeParameter, 
    NodeRequest, 
    ValidationResult,
    ValidationError
)

@dataclass
class NodeResult:
    """
    Represents the result of executing a node.
    
    This class encapsulates the result data, any error that occurred during execution,
    and the index of the next connection to follow in the workflow.
    """
    result: Optional[Any] = None
    error: Optional[str] = None
    next_connection_index: int = 0

class Node(ABC):
    """
    Abstract base class for all nodes in the workflow system.
    
    A node represents a single unit of work in a workflow. Each node has:
    - A description that defines its type, inputs, outputs, and parameters
    - Runtime data that represents its current state in a workflow
    
    Nodes can be connected to other nodes to form a directed graph.
    Each node implementation should define its own execution logic.
    """

    allow_multiple_trigger_values = True

    def __init__(self):
        """
        Initialize a new node instance.
        
        Args:
            description: Metadata describing this node type's capabilities and requirements
            data: Runtime data for this node instance (position, connections, parameter values)
        """
        self.description = self.__class__.get_description()

    @classmethod
    def get_description(cls) -> NodeTypeDescription:
        raise NotImplementedError("Subclasses must implement get_description method")

    def get_parameter_by_name(self, name: str) -> Optional[NodeParameter]:
        """
        Get a parameter definition by name from the node's description.
        
        This helper method makes it easier to work with parameters as a list
        while still allowing lookup by name when needed.
        
        Args:
            name: The name of the parameter to find
            
        Returns:
            Optional[NodeParameter]: The parameter definition if found, None otherwise
        """
        if not self.description or not self.description.parameters:
            return None
            
        for param in self.description.parameters:
            if param.name == name:
                return param
                
        return None

    @abstractmethod
    async def run(self, data: NodeData) -> NodeResult:
        raise NotImplementedError("Subclasses must implement the run method to execute the node logic")

    def base_validate(self, request: NodeRequest) -> ValidationResult:
        """
        Base validation for all node types.
        
        This method performs common validation that applies to all nodes:
        - Validates node description mandatory fields
        - Validates parameter properties (type, name, display_name)
        - Validates parameter value types match expected types
        
        Args:
            request: The node request to validate
            
        Returns:
            ValidationResult: The validation result with any errors found
        """
        errors = []
        
        # Check if node description exists
        if not self.description:
            return ValidationResult(valid=False, errors=[
                ValidationError(parameter="", message="Node description not available for validation")
            ])
        
        # Validate NodeTypeDescription mandatory fields
        if not self.description.name:
            errors.append(ValidationError(parameter="description.name", message="Node type name is required"))
            
        if not self.description.display_name:
            errors.append(ValidationError(parameter="description.display_name", message="Node display name is required"))
            
        if not self.description.description:
            errors.append(ValidationError(parameter="description.description", message="Node description is required"))
            
        if not self.description.group:
            errors.append(ValidationError(parameter="description.group", message="Node group is required"))
            
        if not self.description.inputs:
            errors.append(ValidationError(parameter="description.inputs", message="Node inputs definition is required"))
            
        if not self.description.outputs:
            errors.append(ValidationError(parameter="description.outputs", message="Node outputs definition is required"))
            
        # Validate NodeRequest mandatory fields
        if not request.type:
            errors.append(ValidationError(parameter="node_type", message="Node type is required"))
            
        # if not request.position or len(request.position) != 2:
        #     errors.append(ValidationError(parameter="position", message="Node position must be specified as [x, y]"))
            
        # # Validate parameter properties
        # if self.description.parameters:
        #     for param in self.description.parameters:
        #         # Check parameter has a name
        #         if not param.name:
        #             errors.append(ValidationError(
        #                 parameter="parameters", 
        #                 message="Parameter missing required 'name' property"
        #             ))
        #             continue
                
        #         # Check parameter has a valid type
        #         if not param.type or not isinstance(param.type, PropertyTypes):
        #             errors.append(ValidationError(
        #                 parameter=f"parameters.{param.name}", 
        #                 message=f"Parameter '{param.name}' has invalid or missing type"
        #             ))
                
        #         # Check display_name is present for better UI experience
        #         if not param.display_name:
        #             errors.append(ValidationError(
        #                 parameter=f"parameters.{param.name}", 
        #                 message=f"Parameter '{param.name}' is missing display_name"
        #             ))
                
        #         # If options type, validate that options are provided
        #         if param.type in [PropertyTypes.OPTIONS, PropertyTypes.MULTI_OPTIONS] and (not param.options or len(param.options) == 0):
        #             errors.append(ValidationError(
        #                 parameter=f"parameters.{param.name}", 
        #                 message=f"Parameter '{param.name}' of type {param.type} requires options to be defined"
        #             ))
                
        #         # For parameters with options, validate that each option has name and value
        #         if param.options:
        #             for i, option in enumerate(param.options):
        #                 if not option.name:
        #                     errors.append(ValidationError(
        #                         parameter=f"parameters.{param.name}.options[{i}]", 
        #                         message=f"Option in parameter '{param.name}' is missing name"
        #                     ))
        #                 if option.value is None:
        #                     errors.append(ValidationError(
        #                         parameter=f"parameters.{param.name}.options[{i}]", 
        #                         message=f"Option in parameter '{param.name}' is missing value"
        #                     ))
                
        #         # If this parameter has specific value_type constraints
        #         if param.value_type and param.value_type.types:
        #             # Check if parameter value is provided in the request
        #             if request.parameters and param.name in request.parameters and request.parameters[param.name] is not None:
        #                 value = request.parameters[param.name]
        #                 valid = False
                        
        #                 # Check if value matches any of the allowed types
        #                 for type_name in param.value_type.types:
        #                     if (type_name == PropertyTypes.STRING and isinstance(value, str)) or \
        #                        (type_name == PropertyTypes.NUMBER and isinstance(value, (int, float))) or \
        #                        (type_name == PropertyTypes.BOOLEAN and isinstance(value, bool)):
        #                         valid = True
        #                         break
                                
        #                 if not valid:
        #                     errors.append(ValidationError(
        #                         parameter=param.name, 
        #                         message=f"Value for '{param.name}' has incorrect type. Expected one of {[t for t in param.value_type.types]}"
        #                     ))
        
        return ValidationResult(valid=len(errors) == 0, errors=errors if errors else None)

    @abstractmethod
    def validate(self, request: NodeRequest) -> ValidationResult:
        """
        Validate a node request against this node's schema.
        
        This method must be implemented by all concrete node subclasses to add
        node-specific validation logic. It should call base_validate to perform
        common validation checks and then add any node-specific validation.
        
        Args:
            request: The node request to validate
            
        Returns:
            ValidationResult: The validation result with any errors found
            
        Raises:
            NotImplementedError: If the subclass doesn't implement this method
        """
        # pass
                  
    def get_trigger_value(self, data: Any) -> Optional[str]:
        """
        Get the trigger value for this node.
        
        Args:
            node: The node data
            
        Returns:
            str: The trigger value
        """
        return data.trigger_value
        
    
    def create_trigger_value(self, node: NodeData) -> str:
        """
        Get the trigger value for this node.
        
        Args:
            node: The node data
            
        Returns:
            str: The trigger value
        """
        return ",".join([str(v) for _, v in node.parameters.items()])

    def __str__(self) -> str:
        """String representation of the node."""
        if self.description and hasattr(self.description, "name"):
            return f"Node(type={self.description.name})"
        return "Node(type=Unknown)"
