"""
Node Models Module

This module contains all the model classes used for defining nodes and their properties.
These models provide the type structure for node definitions, parameters, and connections.
"""

from pydantic import BaseModel, Field
from typing import Dict, List, Literal, Optional, Union, Any
from enum import Enum

# from app.models.workflow import WorkflowExecutionStatus

# Type definitions
NodeParameterValue = Union[str, int, float, bool, dict, list, None]
"""Represents possible values for node parameters (equivalent to TypeScript's string | number | boolean | undefined | null | array)"""

NodeGroupType = Literal['input', 'output', 'organization', 'schedule', 'transform', 'trigger', 'database']
"""Defines the possible group categories for nodes"""


class NodeConnectionType(str, Enum):
    """Enumerates the possible connection types between nodes"""
    # AiAgent = "ai_agent"
    # AiChain = "ai_chain"
    # AiDocument = "ai_document"
    # AiEmbedding = "ai_embedding"
    # AiLanguageModel = "ai_languageModel"
    # AiMemory = "ai_memory"
    # AiOutputParser = "ai_outputParser"
    # AiRetriever = "ai_retriever"
    # AiReranker = "ai_reranker"
    # AiTextSplitter = "ai_textSplitter"
    # AiTool = "ai_tool"
    # AiVectorStore = "ai_vectorStore"
    Main = "main"


class PropertyTypes(str, Enum):
    """Enumerates the possible property types for node parameters"""
    BOOLEAN = 'boolean'
    BUTTON = 'button'
    COLLECTION = 'collection'
    COLOR = 'color'
    DATE_TIME = 'dateTime'
    FIXED_COLLECTION = 'fixedCollection'
    HIDDEN = 'hidden'
    JSON = 'json'
    CALLOUT = 'callout'
    NOTICE = 'notice'
    MULTI_OPTIONS = 'multiOptions'
    NUMBER = 'number'
    OPTIONS = 'options'
    STRING = 'string'
    TEXT_VIEW = 'textView'
    CREDENTIALS_SELECT = 'credentialsSelect'
    RESOURCE_LOCATOR = 'resourceLocator'
    CURL_IMPORT = 'curlImport'
    RESOURCE_MAPPER = 'resourceMapper'
    FILTER = 'filter'
    ASSIGNMENT_COLLECTION = 'assignmentCollection'
    CREDENTIALS = 'credentials'
    WORKFLOW_SELECTOR = 'workflowSelector'
    COMPONENT = 'component'
    TAB = 'tab'
    OAUTH2_BUTTON = 'oauth2_button'


class NodePropertyAction(str, Enum):
    """Enumerates the possible actions that can be performed on a node property"""
    OPEN = "open"
    TRIGGER = "trigger"
    EXECUTE = "execute"


class CodeAutocompleteTypes(str, Enum):
    """Enumerates the code autocompletion types available for code editors"""
    NONE = "none"
    PYTHON = "python"
    JSON = "json"
    JAVASCRIPT = "javascript"


class EditorType(str, Enum):
    """Enumerates the editor types available for code parameters"""
    CODE = "code"
    JSON = "json"
    SQL = "sql"


class SQLDialect(str, Enum):
    """Enumerates the SQL dialects available for SQL editors"""
    MYSQL = "mysql"
    POSTGRES = "postgres"
    SQLITE = "sqlite"


# Property Options Models
class CalloutAction(BaseModel):
    """Defines actions available for callout UI elements"""
    label: str
    action: str
    url: Optional[str] = None


class LoadOptionsMethod(BaseModel):
    """Defines a method for loading options dynamically"""
    name: str
    params: Optional[Dict[str, Any]] = None


class ResourceMapperTypeOptions(BaseModel):
    """Configuration for resource mapper type nodes"""
    resource_type: str
    mode: str
    filter: Optional[Dict[str, Any]] = None


class FilterTypeOptions(BaseModel):
    """Configuration for filter type nodes"""
    type: str
    properties: Optional[Dict[str, Any]] = None


class AssignmentTypeOptions(BaseModel):
    """Configuration for assignment type nodes"""
    values: Dict[str, Any]

class LoadOptions(BaseModel):
    function: str
    # TODO: Response structure need to define or not?

class StyleOptions(BaseModel):
    """
    Common style configuration options for UI elements.
    Can be applied to various component types to control their appearance.
    """
    font_size: Optional[str] = None
    font_weight: Optional[Literal["normal", "bold", "100", "200", "300", "400", "500", "600", "700", "800", "900"]] = None
    font_style: Optional[Literal["normal", "italic", "oblique"]] = None
    color: Optional[str] = None
    background_color: Optional[str] = None
    border: Optional[str] = None
    border_radius: Optional[str] = None
    padding: Optional[str] = None
    margin: Optional[str] = None
    text_align: Optional[Literal["left", "center", "right", "justify"]] = None
    width: Optional[str] = None
    height: Optional[str] = None
    max_width: Optional[str] = None
    max_height: Optional[str] = None
    overflow: Optional[Literal["visible", "hidden", "scroll", "auto"]] = None
    text_transform: Optional[Literal["uppercase", "lowercase", "capitalize", "none"]] = None
    display: Optional[Literal["block", "inline", "inline-block", "flex", "grid", "none"]] = None
    flex: Optional[str] = None
    opacity: Optional[float] = None
    box_shadow: Optional[str] = None

class PropertyTypeOptions(BaseModel):
    """
    Advanced configuration options for node properties.
    This allows fine-grained control over how properties are rendered and behave.
    """
    button_config: Optional[Dict[str, Any]] = None
    container_class: Optional[str] = None
    always_open_edit_window: Optional[bool] = None
    code_autocomplete: Optional[CodeAutocompleteTypes] = None
    editor: Optional[EditorType] = None
    editor_is_read_only: Optional[bool] = None
    sql_dialect: Optional[SQLDialect] = None
    load_options_depends_on: Optional[List[str]] = None
    load_options_method: Optional[str] = None # TODO: Remove this if not needed
    load_options: Optional[LoadOptions] = None # TODO: change to str or keep as is?
    max_value: Optional[float] = None
    min_value: Optional[float] = None
    multiple_values: Optional[bool] = None
    multiple_value_button_text: Optional[str] = None
    number_precision: Optional[int] = None
    password: Optional[bool] = None
    rows: Optional[int] = None
    show_alpha: Optional[bool] = None
    sortable: Optional[bool] = None
    expirable: Optional[bool] = None
    resource_mapper: Optional[ResourceMapperTypeOptions] = None
    filter: Optional[FilterTypeOptions] = None
    assignment: Optional[AssignmentTypeOptions] = None
    min_required_fields: Optional[int] = None
    max_allowed_fields: Optional[int] = None
    callout_action: Optional[CalloutAction] = None
    style: Optional[StyleOptions] = None


# Display condition for conditional field visibility
class DisplayCondition(BaseModel):
    """
    Represents a condition that determines when a field should be displayed.
    Similar to the DisplayCondition type in TypeScript interface.
    """
    _cnd: Dict[str, Any]  # Holds condition info like eq, not, gte, etc.

    @classmethod
    def equals(cls, value: NodeParameterValue) -> 'DisplayCondition':
        """Create an equals condition"""
        return cls(_cnd={"eq": value})

    @classmethod
    def not_equals(cls, value: NodeParameterValue) -> 'DisplayCondition':
        """Create a not equals condition"""
        return cls(_cnd={"not": value})

    @classmethod
    def greater_than_equals(cls, value: Union[int, float, str]) -> 'DisplayCondition':
        """Create a greater than or equals condition"""
        return cls(_cnd={"gte": value})

    @classmethod
    def less_than_equals(cls, value: Union[int, float, str]) -> 'DisplayCondition':
        """Create a less than or equals condition"""
        return cls(_cnd={"lte": value})


class DisplayOptions(BaseModel):
    """
    Configuration for conditionally showing or hiding fields.
    Follows the IDisplayOptions interface structure from TypeScript.
    
    In n8n, parameters are defined as a list rather than a dictionary.
    For example:
    
    display_options = DisplayOptions(
        show={
            "resume_type": ["time_interval", DisplayCondition.equals("other_value")]
        }
    )
    """
    hide: Optional[Dict[str, List[Union[NodeParameterValue, DisplayCondition]]]] = None
    show: Optional[Dict[str, List[Union[NodeParameterValue, DisplayCondition]]]] = None
    hide_on_cloud: Optional[bool] = None


# Credentials Models
class CredentialsDisplayOptions(BaseModel):
    """Defines display options for credentials"""
    hide: Optional[Dict[str, List[Any]]] = None
    show: Optional[Dict[str, List[Any]]] = None


class NodeCredentialDescription(BaseModel):
    """Defines credential requirements for a node"""
    name: str
    display_name: Optional[str] = None
    required: Optional[bool] = False
    display_options: Optional[CredentialsDisplayOptions] = None

class NodeCredential(BaseModel):
    id: str
    name: str

# Node Parameter Models
class NodeParameterOption(BaseModel):
    """
    Options for node parameters, typically used in dropdown selects.
    """
    name: str
    value: NodeParameterValue
    description: Optional[str] = None
    action: Optional[str] = None
    icon: Optional[str] = None
    
    def dict(self, *args, **kwargs):
        """Filter out null values from the option"""
        result = super().model_dump(*args, **kwargs)
        return {k: v for k, v in result.items() if v is not None}


class NodeParameterValueType(BaseModel):
    """
    Defines the acceptable types for a parameter's value.
    """
    mode: str = "multiple"  # "multiple" or "single"
    types: List[PropertyTypes]
    

class NodeParameter(BaseModel):
    """
    Complete definition of a node parameter including its type,
    display properties, validation rules, and available options.
    """
    name: str
    type: PropertyTypes
    value_type: Optional[NodeParameterValueType] = None
    default: Optional[NodeParameterValue] = None
    display_name: Optional[str] = None
    description: Optional[str] = None
    placeholder: Optional[str] = None
    required: bool = False
    display_options: Optional[DisplayOptions] = None
    options: Optional[List[NodeParameterOption]] = None
    type_options: Optional[PropertyTypeOptions] = None
    depends_on: Optional[List[str]] = None
    default_mode: Optional[str] = None
    sensitive: Optional[bool] = None
    credential_type: Optional[str] = None
    
    def dict(self, *args, **kwargs):
        """
        Return dict representation with nulls and empty containers filtered out
        """
        try:
            result = super().model_dump(*args, **kwargs)
        except AttributeError:
            result = super().dict(*args, **kwargs)
        
        filtered = {}
        for k, v in result.items():
            # Skip null values
            if v is None:
                continue
                
            # Skip empty lists
            if isinstance(v, list) and len(v) == 0:
                continue
                
            # Skip empty dictionaries
            if isinstance(v, dict) and len(v) == 0:
                continue
                
            # Handle nested objects
            if isinstance(v, list) and all(isinstance(item, BaseModel) for item in v):
                # List of models
                filtered[k] = [item.model_dump() for item in v]
            elif isinstance(v, BaseModel):
                # Single model
                nested_dict = v.model_dump()
                if isinstance(nested_dict, dict) and len(nested_dict) > 0:
                    filtered[k] = nested_dict
            else:
                filtered[k] = v
                
        return filtered


# Node Connection and Data Models
class NodeConnection(BaseModel):
    """
    Represents the connection structure between nodes.
    The 'main' property contains a list of lists where each inner list
    contains the indices of nodes that are connected to this node.
    """
    main: Optional[List[List[str]]] = None

class NodeStatus(str, Enum):
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"

class NodeRequest(BaseModel):
    """
    Represents a request to create or update a node.
    Contains the values and configuration for a specific node instance.
    
    This model is used for validating user input when creating or updating nodes,
    and includes fields for position and active status that aren't in NodeData.
    """
    name: str
    type: str
    display_properties: Optional[Dict[str, Any]] = None
    is_active: bool = True
    is_trigger: bool = False
    parameters: Dict[str, NodeParameterValue] = {}
    credentials: Optional[Dict[str, NodeCredential]] = None

class NodeData(NodeRequest):
    """
    Represents the runtime data of a node within a workflow.
    Contains information such as the node's identity, position,
    parameters, and connections to other nodes.
    
    Note: While parameter definitions in NodeTypeDescription are stored as a list
    to match n8n's structure, the runtime parameter values in NodeData are still
    stored as a dictionary for efficient lookup by parameter name.
    """
    status: Optional[NodeStatus] = None
    result: Optional[Any] = None
    input_data: Optional[Any] = None
    error: Optional[str] = None
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    execution_time: Optional[int] = None


class NodeTypeDescription(BaseModel):
    """
    Comprehensive description of a node type.
    Defines how the node appears in the UI, what parameters it accepts,
    its input/output capabilities, and more.
    
    Following n8n's structure, parameters are defined as a list of NodeParameter
    objects rather than a dictionary, ensuring consistent ordering and display.
    """
    name: str
    display_name: str
    description: str
    icon: Optional[str] = None
    icon_color: Optional[str] = None
    icon_url: Optional[str] = None
    group: List[NodeGroupType]
    documentation_url: Optional[str] = None
    subtitle: Optional[str] = None
    version: Union[float, List[float]]
    parameters: Optional[List[NodeParameter]] = None
    inputs: Optional[List[NodeConnectionType]] = None
    outputs: Optional[List[NodeConnectionType]] = None
    default_version: Optional[str] = None
    hidden: bool = False
    input_names: Optional[List[str]] = None
    output_names: Optional[List[str]] = None
    credentials: Optional[List[NodeCredentialDescription]] = None
    
    def dict(self, *args, **kwargs):
        """
        Return dict representation with nulls and empty containers filtered out
        """
        try:
            return super().model_dump(*args, **kwargs, exclude_none=True)
        except AttributeError:
           return super().dict(*args, **kwargs, exclude_none=True)


class ValidationError(BaseModel):
    """
    Represents a validation error for a node parameter.
    """
    parameter: str
    message: str


class ValidationResult(BaseModel):
    """
    Represents the result of validating a node request.
    """
    valid: bool
    errors: Optional[List[ValidationError]] = None

class NodeFunctionRequest(BaseModel):
    node_request: NodeRequest
    function: str


class WorkflowStatus(str, Enum):
    """Enum for workflow status values"""
    DRAFT = "draft"
    PUBLISHED = "published"
    ARCHIVED = "archived"
    DEPRECATED = "deprecated"

class WorkflowExecutionStatus(str,Enum):
    """
    Enum for workflow execution statuses.
    """
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"


class WorkflowModel(BaseModel):
    """
    Represents a workflow model containing nodes, connections, and metadata.
    
    This model is used to define workflows in a structured way, including
    the nodes involved, their parameters, and how they are connected.
    """
    id: str
    name: str
    display_name: str
    description: str
    is_active: bool = True
    status: WorkflowStatus = WorkflowStatus.DRAFT
    version: str = "0.0.1"
    initial_data: Dict[str, Any] = Field(default_factory=dict)
    start_node: List[str] = Field(
        default_factory=list, 
        description="List of node names that can be used as starting points for the workflow"
    )
    nodes: Dict[str, NodeData] = Field(default_factory=dict)
    connections: Dict[str, NodeConnection] = Field(default_factory=dict)

class WorkflowExecutionUpdate(BaseModel):
    execution_id: str
    status: Optional[WorkflowExecutionStatus] = None
    error_message: Optional[str] = None
    duration_seconds: Optional[int] = None
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    billable_duration_seconds: Optional[int] = None
    result: Optional[Dict[str, Any]] = None
