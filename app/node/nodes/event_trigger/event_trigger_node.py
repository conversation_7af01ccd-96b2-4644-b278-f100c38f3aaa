from typing import Optional
from app.node.node_base.node_models import LoadOptions, NodeConnectionType, NodeData, NodeParameter, NodeRequest, NodeTypeDescription, PropertyTypeOptions, PropertyTypes, ValidationResult
from app.node.node_utils.workflow_defn import node_defn
from app.node.node_base.node import Node, NodeResult
from app.node.nodes.mongodb.operations.base_operation import MongoDBConnectionManager
from motor.motor_asyncio import AsyncIOMotorDatabase


@node_defn(type='event_trigger', is_activity=False)
class EventTriggerNode(Node):
    """Node that triggers a workflow manually."""

    async def _get_mongodb(self) -> AsyncIOMotorDatabase:
        from app.core.database import get_mongodb
        """Get MongoDB database."""
        return await get_mongodb()

    async def get_event_types(self, request: NodeRequest) -> list[dict]:
        """
        Get available event types from MongoDB.
        
        Args:
            request: The node request object
            db: MongoDB collection (injected by decorator)
            
        Returns:
            list[dict]: List of event types with name and value
        """
        try:
            db = await self._get_mongodb()
            if db is None:
                raise ValueError("MongoDB database is not connected")
            event_types = []
            async for event_type in db['event_type'].find({}, {'name': 1,  '_id': 1}):
                event_types.append({
                    "name": event_type['name'],
                    "value": str(event_type['_id'])
                })
            return event_types
        except Exception as e:
            # Log the error appropriately
            return []  # Return empty list as fallback

    @classmethod
    def get_description(cls) -> NodeTypeDescription:
        return NodeTypeDescription(
            name="event_trigger",
            display_name="Event Trigger",
            description="Runs the flow on a specific event triggered by an external system.",
            icon="fa:mouse-pointer",
            icon_color="#909298",
            group=["trigger"],
            version=1,
            inputs=[],
            outputs=[NodeConnectionType.Main],
            parameters=[
                NodeParameter(
                    name="event_type",
                    display_name="Event Type",
                    type=PropertyTypes.OPTIONS,
                    default="event_type_1",
                    required=True,
                    type_options=PropertyTypeOptions(
                        load_options=LoadOptions(function="get_event_types")
                    ),
                )
            ],
        )
    
    async def run(self, request: NodeData) -> NodeResult:
        """ Run the manual trigger node.
        This node does not perform any action but serves as a starting point for the workflow.
        """
        return NodeResult(
            result=request.input_data,
            next_connection_index=0
        )
    
    def validate(self, request: NodeData) -> ValidationResult:
        """
        Validate the node request.
        This node does not require any specific validation.
        """
        return ValidationResult(
            valid=True,
            errors=[],
        )
