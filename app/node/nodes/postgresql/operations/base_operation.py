"""
Base Operation Module

This module provides base functionality and utilities for PostgreSQL operations
including connection management, error handling, and common database utilities.
"""

import asyncio
import logging
from typing import Dict, Any, List
from contextlib import asynccontextmanager

import asyncpg
from app.node.node_base.node_models import NodeData

# Set up logging
logger = logging.getLogger(__name__)


class PostgreSQLConnectionManager:
    """
    PostgreSQL connection manager with connection pooling.
    
    Provides secure connection management, connection pooling,
    and proper resource cleanup for PostgreSQL operations.
    """
    
    @classmethod
    @asynccontextmanager
    async def get_connection(cls, data: NodeData):
        """
        Get a database connection from the pool.

        Args:
            data: Node execution data

        Yields:
            Connection: PostgreSQL connection
        """
        if not data.credentials or 'postgresql' not in data.credentials:
            raise ValueError("PostgreSQL credentials are required")

        from app.credential.utils.credential_manager import CredentialManager

        credential_manager = CredentialManager()
        auth_generator = await credential_manager.get_custom_authentication(data.credentials['postgresql'].id, "postgresql")

        try:
            async for connection in auth_generator:
                yield connection
                break  # We only need the first connection
        except Exception as e:
            logger.error(f"Failed to connect to PostgreSQL: {str(e)}")
            raise
    
class BaseOperation:
    """
    Base class for PostgreSQL operations.
    
    Provides common functionality for database operations including
    error handling, parameter validation, and result formatting.
    """
    
    @staticmethod
    def format_error(error: Exception, operation: str) -> str:
        """
        Format database errors into user-friendly messages.
        
        Args:
            error: The exception that occurred
            operation: The operation being performed
            
        Returns:
            str: Formatted error message
        """
        if isinstance(error, asyncpg.PostgresError):
            return f"PostgreSQL {operation} error: {str(error)}"
        # elif isinstance(error, asyncpg.ConnectionError):
        #     return f"PostgreSQL connection error during {operation}: {str(error)}"
        elif isinstance(error, asyncio.TimeoutError):
            return f"PostgreSQL {operation} operation timed out"
        else:
            return f"PostgreSQL {operation} operation failed: {str(error)}"
    
    @staticmethod
    def format_results(records: List[Any], operation: str) -> Dict[str, Any]:
        """
        Format database results into a standardized structure.
        
        Args:
            records: Database query results
            operation: The operation that was performed
            
        Returns:
            Dict containing formatted results
        """
        if not records:
            return {
                "operation": operation,
                "rows_affected": 0,
                "data": []
            }
        
        # Convert asyncpg.Record objects to dictionaries
        formatted_records = []
        for record in records:
            if hasattr(record, 'items'):
                # asyncpg.Record has items() method
                formatted_records.append(dict(record))
            elif isinstance(record, dict):
                formatted_records.append(record)
            else:
                # Handle other types
                formatted_records.append(record)
        
        return {
            "operation": operation,
            "rows_affected": len(formatted_records),
            "data": formatted_records
        }
    
    @staticmethod
    def validate_table_name(table_name: Any) -> bool:
        """
        Validate table name for SQL injection prevention.
        
        Args:
            table_name: Table name to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        if not table_name or not isinstance(table_name, str):
            return False
        
        # Basic validation - alphanumeric, underscore, and dots for schema.table
        import re
        pattern = r'^[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z_][a-zA-Z0-9_]*)?$'
        return bool(re.match(pattern, table_name))
    
    @staticmethod
    def escape_identifier(identifier: str) -> str:
        """
        Escape SQL identifiers to prevent injection.
        
        Args:
            identifier: SQL identifier to escape
            
        Returns:
            str: Escaped identifier
        """
        # Remove any existing quotes and add double quotes
        escaped = identifier.replace('"', '""')
        return f'"{escaped}"'
