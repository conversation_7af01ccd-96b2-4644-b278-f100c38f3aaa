"""
PostgreSQL SELECT Operation

This module implements the SELECT operation for PostgreSQL nodes,
providing comprehensive query capabilities with filtering, sorting, and pagination.
"""

import logging
from typing import Any, List

from app.node.node_base.node import NodeResult
from app.node.node_base.node_models import NodeData
from .base_operation import BaseOperation, PostgreSQLConnectionManager

# Set up logging
logger = logging.getLogger(__name__)


class SelectOperation(BaseOperation):
    """
    PostgreSQL SELECT operation implementation.
    
    Provides comprehensive SELECT query capabilities including:
    - Column selection with wildcard support
    - WHERE clause filtering with parameter binding
    - ORDER BY sorting with multiple columns
    - LIMIT and OFFSET pagination
    - JOIN operations
    - Proper SQL injection prevention
    """
    
    @staticmethod
    async def execute(data: NodeData) -> NodeResult:
        """
        Execute a SELECT operation on PostgreSQL.
        
        Args:
            data: Node execution data containing query parameters
            
        Returns:
            NodeResult: Query results or error information
        """
        try:
            # Extract parameters
            schema = str(data.parameters.get('schema', 'public'))
            table = str(data.parameters.get('table'))
            columns = str(data.parameters.get('columns', '*'))
            where_clause = str(data.parameters.get('where_clause'))
            order_by = str(data.parameters.get('order_by'))
            limit = data.parameters.get('limit')
            offset = data.parameters.get('offset')
            timeout = data.parameters.get('timeout', 30)
            
            # Validate required parameters
            if not table:
                return NodeResult(error="Table name is required for SELECT operation")
            
            # Validate table name
            if not SelectOperation.validate_table_name(table):
                return NodeResult(error=f"Invalid table name: {table}")
            
            # Build the SELECT query
            query_builder = SelectQueryBuilder(schema, table)

            # Add columns
            if columns and columns.strip() != '*':
                query_builder.add_columns(columns)
            
            # Add WHERE clause
            if where_clause:
                query_builder.add_where_clause(where_clause)
            
            # Add ORDER BY
            if order_by:
                query_builder.add_order_by(order_by)
            
            # Add LIMIT and OFFSET
            if limit and isinstance(limit, str):
                query_builder.add_limit(int(limit))
            
            if offset and isinstance(offset, str):
                query_builder.add_offset(int(offset))
            
            # Build final query
            query, params = query_builder.build()
            
            logger.info(f"Executing SELECT query: {query}")
            logger.debug(f"Query parameters: {params}")
            
            # Execute query
            async with PostgreSQLConnectionManager.get_connection(data) as connection:

                # Set query timeout
                if timeout and isinstance(timeout, int) and timeout > 0:
                    records = await connection.fetch(query, *params, timeout=timeout)
                else:
                    records = await connection.fetch(query, *params)
                
                # Format results
                result_data = SelectOperation.format_results(records, "SELECT")
                
                logger.info(f"SELECT operation completed: {result_data['rows_affected']} rows returned")
                
                return NodeResult(result=result_data)
                
        except Exception as e:
            error_msg = SelectOperation.format_error(e, "SELECT")
            logger.error(error_msg, exc_info=True)
            return NodeResult(error=error_msg)


class SelectQueryBuilder:
    """
    Builder class for constructing SELECT queries safely.
    
    Provides a fluent interface for building SELECT queries with
    proper parameter binding and SQL injection prevention.
    """
    
    def __init__(self, schema: str, table: str):
        """
        Initialize the query builder.
        
        Args:
            schema: Database schema name
            table: Table name
        """
        self.schema = schema
        self.table = table
        self.columns = ["*"]
        self.where_conditions = []
        self.order_by_clauses = []
        self.limit_value = None
        self.offset_value = None
        self.parameters = []
    
    def add_columns(self, columns: str) -> 'SelectQueryBuilder':
        """
        Add columns to select.
        
        Args:
            columns: Comma-separated column names
            
        Returns:
            SelectQueryBuilder: Self for method chaining
        """
        if columns and columns.strip() != '*':
            # Parse and validate column names
            column_list = [col.strip() for col in columns.split(',')]
            validated_columns = []
            
            for col in column_list:
                if col and self._is_valid_column_name(col):
                    validated_columns.append(col)
                else:
                    logger.warning(f"Skipping invalid column name: {col}")
            
            if validated_columns:
                self.columns = validated_columns
        
        return self
    
    def add_where_clause(self, where_clause: str) -> 'SelectQueryBuilder':
        """
        Add WHERE clause to the query.
        
        Args:
            where_clause: WHERE condition
            
        Returns:
            SelectQueryBuilder: Self for method chaining
        """
        if where_clause and where_clause.strip():
            # For now, add the WHERE clause as-is
            # In a production system, you'd want more sophisticated parsing
            # and parameter binding for user-provided WHERE clauses
            self.where_conditions.append(where_clause.strip())
        
        return self
    
    def add_order_by(self, order_by: str) -> 'SelectQueryBuilder':
        """
        Add ORDER BY clause to the query.
        
        Args:
            order_by: ORDER BY specification
            
        Returns:
            SelectQueryBuilder: Self for method chaining
        """
        if order_by and order_by.strip():
            # Parse ORDER BY clause
            order_parts = [part.strip() for part in order_by.split(',')]
            validated_order = []
            
            for part in order_parts:
                if self._is_valid_order_by_clause(part):
                    validated_order.append(part)
                else:
                    logger.warning(f"Skipping invalid ORDER BY clause: {part}")
            
            if validated_order:
                self.order_by_clauses.extend(validated_order)
        
        return self
    
    def add_limit(self, limit: int) -> 'SelectQueryBuilder':
        """
        Add LIMIT to the query.
        
        Args:
            limit: Maximum number of rows to return
            
        Returns:
            SelectQueryBuilder: Self for method chaining
        """
        if limit and limit > 0:
            self.limit_value = limit
        
        return self
    
    def add_offset(self, offset: int) -> 'SelectQueryBuilder':
        """
        Add OFFSET to the query.
        
        Args:
            offset: Number of rows to skip
            
        Returns:
            SelectQueryBuilder: Self for method chaining
        """
        if offset and offset >= 0:
            self.offset_value = offset
        
        return self
    
    def build(self) -> tuple[str, List[Any]]:
        """
        Build the final SELECT query.
        
        Returns:
            Tuple of (query_string, parameters)
        """
        # Build SELECT clause
        if self.columns == ["*"]:
            select_clause = "SELECT *"
        else:
            # Escape column names
            escaped_columns = [BaseOperation.escape_identifier(col) for col in self.columns]
            select_clause = f"SELECT {', '.join(escaped_columns)}"
        
        # Build FROM clause
        escaped_schema = BaseOperation.escape_identifier(self.schema)
        escaped_table = BaseOperation.escape_identifier(self.table)
        from_clause = f"FROM {escaped_schema}.{escaped_table}"
        
        # Build WHERE clause
        where_clause = ""
        if self.where_conditions:
            where_clause = f"WHERE {' AND '.join(self.where_conditions)}"
        
        # Build ORDER BY clause
        order_clause = ""
        if self.order_by_clauses:
            order_clause = f"ORDER BY {', '.join(self.order_by_clauses)}"
        
        # Build LIMIT clause
        limit_clause = ""
        if self.limit_value:
            limit_clause = f"LIMIT {self.limit_value}"
        
        # Build OFFSET clause
        offset_clause = ""
        if self.offset_value:
            offset_clause = f"OFFSET {self.offset_value}"
        
        # Combine all clauses
        query_parts = [select_clause, from_clause]
        if where_clause:
            query_parts.append(where_clause)
        if order_clause:
            query_parts.append(order_clause)
        if limit_clause:
            query_parts.append(limit_clause)
        if offset_clause:
            query_parts.append(offset_clause)
        
        query = " ".join(query_parts)
        
        return query, self.parameters
    
    def _is_valid_column_name(self, column: str) -> bool:
        """
        Validate column name for SQL injection prevention.
        
        Args:
            column: Column name to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        if not column or not isinstance(column, str):
            return False
        
        # Allow alphanumeric, underscore, and dots for table.column
        import re
        pattern = r'^[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z_][a-zA-Z0-9_]*)?$'
        return bool(re.match(pattern, column))
    
    def _is_valid_order_by_clause(self, clause: str) -> bool:
        """
        Validate ORDER BY clause for SQL injection prevention.
        
        Args:
            clause: ORDER BY clause to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        if not clause or not isinstance(clause, str):
            return False
        
        # Allow column name optionally followed by ASC or DESC
        import re
        pattern = r'^[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z_][a-zA-Z0-9_]*)?\s*(ASC|DESC)?$'
        return bool(re.match(pattern, clause.upper()))


# Export the execute function for use by the main node
async def execute(data: NodeData) -> NodeResult:
    """Execute SELECT operation."""
    return await SelectOperation.execute(data)
