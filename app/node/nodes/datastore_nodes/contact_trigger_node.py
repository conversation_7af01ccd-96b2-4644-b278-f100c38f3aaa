from datetime import datetime
from typing import Dict, Any, Optional, List
from app.node.node_base.node_models import NodeConnectionType, NodeParameter, NodeParameterOption, NodeRequest, NodeTypeDescription, PropertyTypes, ValidationResult
from app.node.node_utils.workflow_defn import node_defn
from app.node.node_base.node import Node, NodeResult
from app.node.node_base.node_models import NodeData
from motor.motor_asyncio import AsyncIOMotorDatabase


@node_defn(type='contact_trigger', is_activity=False)
class ContactTriggerNode(Node):
    """Node that triggers a workflow when contact events occur."""

    async def _get_mongodb(self) -> AsyncIOMotorDatabase:
        from app.core.database import get_mongodb
        """Get MongoDB database."""
        return await get_mongodb()

    @classmethod
    def get_description(cls) -> NodeTypeDescription:
        return NodeTypeDescription(
            name="contact_trigger",
            display_name="Contact Trigger",
            description="Triggers workflow when contact events occur (create, update, delete)",
            icon="fa:address-book",
            icon_color="#4CAF50",
            group=["trigger"],
            version=1,
            inputs=[],
            outputs=[NodeConnectionType.Main],
            parameters=[
                NodeParameter(
                    name="trigger_contact_event",
                    display_name="Trigger Event",
                    description="When should this workflow be triggered",
                    type=PropertyTypes.OPTIONS,
                    required=True,
                    default="contact_created",
                    options=[
                        NodeParameterOption(
                            name="On Create", 
                            value="on_create", 
                            description="Trigger when a new contact is created"
                        ),
                        NodeParameterOption(
                            name="On Update", 
                            value="on_update", 
                            description="Trigger when a contact is updated"
                        ),
                        NodeParameterOption(
                            name="On Delete", 
                            value="on_delete", 
                            description="Trigger when a contact is deleted"
                        )
                    ]
                )
            ],
        )
    
    def _serialize_datetime_fields(self, data: dict) -> dict:
        """
        Convert datetime objects in the data to ISO format strings.
        
        Args:
            data: Dictionary that may contain datetime objects
            
        Returns:
            Dictionary with datetime objects converted to strings
        """
        serialized_data = {}
        for key, value in data.items():
            if isinstance(value, datetime):
                serialized_data[key] = value.isoformat()
            elif isinstance(value, dict):
                serialized_data[key] = self._serialize_datetime_fields(value)
            elif isinstance(value, list):
                serialized_data[key] = [
                    self._serialize_datetime_fields(item) if isinstance(item, dict) 
                    else (item.isoformat() if isinstance(item, datetime) else item)
                    for item in value
                ]
            else:
                serialized_data[key] = value
        return serialized_data

    async def run(self, data: NodeData) -> NodeResult:
        """
        Process the contact event data when triggered.
        This node receives data from the contact event handler and passes it to the workflow.
        
        Args:
            data: Node data containing contact event information
            
        Returns:
            NodeResult with the processed contact data
        """
        try:
            # Get the contact data from the input
            contact_data = data.input_data
            if not contact_data:
                return NodeResult(
                    error="No contact data received"
                )

            serialized_data = self._serialize_datetime_fields(contact_data)
            # Return the contact data to be processed by the workflow
            return NodeResult(
                result=serialized_data,
                next_connection_index=0
            )
        except Exception as e:
            return NodeResult(
                error=f"Error processing contact trigger: {str(e)}"
            )
    
    def validate(self, request: NodeData) -> ValidationResult:
        """
        Validate the contact trigger node configuration.
        
        Args:
            request: Node request containing configuration parameters
            
        Returns:
            ValidationResult with validation status
        """
        # Basic validation is sufficient as the node mainly listens for events
        return ValidationResult(
            valid=True,
            errors=[],
        )