

from typing import Any, Dict, List, Union
from app.node.node_base.node import <PERSON><PERSON><PERSON><PERSON><PERSON>
from app.node.node_base.node_models import Node<PERSON><PERSON>, NodeRequest


async def _get_collection(schema):
        """Get the collection for the current schema."""
        if not schema:
            raise ValueError("Schema name not set")
        from app.core.database import get_mongodb
        db = await get_mongodb()
        return db[schema]

async def execute_read_operation_datastore(schema: str, data: NodeRequest) -> dict:
    """Execute the read operation on the datastore."""
    query = {}
    if 'condition' in data.parameters:
        query = data.parameters['condition']
            
    limit = int(str(data.parameters.get('limit', 10)))
    skip = int(str(data.parameters.get('offset', 0)))
    sort_param = data.parameters.get('sort', {})
    if not isinstance(sort_param, dict):
        sort_param = {}
    
    cursor = (await _get_collection(schema)).find(query).sort(sort_param).skip(skip).limit(limit)     
    result = await cursor.to_list(length=limit)

    return {
            'data': result,
            'metadata': {
                'total': len(result),
                'limit': limit,
                'offset': skip
            }
        }

async def execute_create_operation_datastore(schema: str, data: NodeRequest) -> dict:
    """Execute the create operation on the datastore."""
    if not data.parameters.get('data'):
        return {'error': "Data is required for create operation"}
    if not schema:
        return {'error': "Schema is required for create operation"}
    try:
        # Get collection
        collection = await _get_collection(schema)
            
        insert_data = data.parameters['data']
        
        # Handle single or multiple documents
        if isinstance(insert_data, list):
            result = await collection.insert_many(insert_data)
            inserted_ids = result.inserted_ids
        else:
            result = await collection.insert_one(insert_data)
            inserted_ids = [result.inserted_id]
        
        if isinstance(inserted_ids, list):
            list_inserted_ids = [str(id) for id in inserted_ids]
            list_inserted_count = len(inserted_ids)
        else:
            list_inserted_ids = str(inserted_ids)
            list_inserted_count = 1

        return {
            'success': True,
            'data': {
                'inserted_ids': list_inserted_ids,
                'inserted_count': list_inserted_count
            }
        }
    except Exception as e:
        return {'error': str(e)}

async def execute_update_operation_datastore(schema: str, data: NodeRequest) -> dict:
    """Execute the update operation on the datastore."""
    try:
        # Get collection
        collection = await _get_collection(schema)
        
        # Validate required parameters
        if not data.parameters.get('data'):
            return {'error': 'Update data is required'}
            
        if not data.parameters.get('condition'):
            return {'error': 'Update condition is required'}
            
        # Ensure condition is a dictionary
        if not isinstance(data.parameters['condition'], dict):
            return {'error': 'Update condition must be a dictionary'}
            
        # Prepare update parameters
        query = data.parameters['condition']
        update_data = {'$set': data.parameters['data']}
        
        # Execute update operation
        result = await collection.update_many(
            filter=query,
            update=update_data
        )
        
        return {
            'success': True,
            'data': {
                'matched_count': result.matched_count,
                'modified_count': result.modified_count,
                'upserted_id': str(result.upserted_id) if result.upserted_id else None
            }
        }
    except Exception as e:
        return {'error': str(e)}
