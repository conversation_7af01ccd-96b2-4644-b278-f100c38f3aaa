from datetime import datetime
import json
from typing import Any, Callable, Dict, List
from temporalio import workflow
from app.node.node_base.node_models import DisplayOptions, LoadOptions, NodeConnectionType, NodeCredential, NodeParameter, NodeParameterOption, NodeData, NodeRequest, NodeTypeDescription, PropertyTypeOptions, PropertyTypes, ValidationError, ValidationResult
from app.node.node_utils.workflow_defn import node_defn
from app.node.node_base.node import Node, NodeResult

from app.node.nodes.mongodb.operations.base_operation import MongoDBConnectionManager

from app.node.nodes.mongodb.operations.find_operation import FindOperation
from app.node.nodes.mongodb.operations.insert_operation import InsertOperation
from app.node.nodes.mongodb.operations.update_operation import UpdateOperation
from app.node.nodes.mongodb.operations.delete_operation import DeleteOperation
from app.node.nodes.mongodb.operations.aggregate_operation import AggregateOperation
from app.node.nodes.mongodb.operations.find_and_modify_operation import FindAndModifyOperation
from app.node.nodes.mongodb.operations.count_operation import CountOperation

with workflow.unsafe.imports_passed_through():
    from motor.motor_asyncio import AsyncIOMotorClient
    from app.core.config import settings
    from app.core.database import get_mongodb
    import asyncio
    

@node_defn(type='datastore', is_activity=True)
class DataStoreNode(Node):
    """Datastore Node for creating and managing datastore records."""

    @classmethod
    def get_description(cls) -> NodeTypeDescription:
        return NodeTypeDescription(
            name="datastore",
            display_name="Datastore",
            description="Create or update datastore records",
            icon="fa:database",
            icon_color="#909298",
            group=["database"],
            version=1,
            inputs=[],
            parameters= [
                NodeParameter(
                    name="operation",
                    display_name="Operation",
                    description="The database operation to perform",
                    type=PropertyTypes.OPTIONS,
                    required=True,
                    default="find",
                    options=[
                        NodeParameterOption(name="Find", value="find", description="Find documents in collection"),
                        NodeParameterOption(name="Insert", value="insert", description="Insert documents into collection"),
                        NodeParameterOption(name="Update", value="update", description="Update documents in collection"),
                        NodeParameterOption(name="Delete", value="delete", description="Delete documents from collection"),
                        NodeParameterOption(name="Aggregate", value="aggregate", description="Perform aggregation pipeline"),
                        NodeParameterOption(name="Find One and Update", value="findOneAndUpdate", description="Find and update a single document"),
                        NodeParameterOption(name="Find One and Replace", value="findOneAndReplace", description="Find and replace a single document"),
                        NodeParameterOption(name="Count", value="count", description="Count documents in collection")
                    ]
                ),
                NodeParameter(
                    name="collection",
                    display_name="Schema",
                    description="Datastore collection name",
                    type=PropertyTypes.STRING,
                    required=True,
                    placeholder="users",
                    default="",
                    display_options=DisplayOptions(
                        show={"operation": ["find", "insert", "update", "delete", "aggregate", "findOneAndUpdate", "findOneAndReplace", "count"]}
                    ),
                    type_options=PropertyTypeOptions(
                        load_options=LoadOptions(function="get_datastore_collections")
                    )
                ),
                NodeParameter(
                    name="query",
                    display_name="Query (JSON Format)",
                    description="DataStore query filter in JSON format",
                    type=PropertyTypes.JSON,
                    required=False,
                    default="{}",
                    placeholder='{"name": "John", "age": {"$gte": 18}}',
                    display_options=DisplayOptions(
                        show={
                            "operation": ["find", "update", "delete", "findOneAndUpdate", "findOneAndReplace", "count"]
                        }
                    )
                ),
                # Aggregation pipeline (for aggregate operation)
                NodeParameter(
                    name="pipeline",
                    display_name="Aggregation Pipeline (JSON Format)",
                    description="DataStore aggregation pipeline in JSON format",
                    type=PropertyTypes.JSON,
                    required=True,
                    default="[]",
                    placeholder='[{"$match": {"status": "active"}}, {"$group": {"_id": "$category", "count": {"$sum": 1}}}]',
                    display_options=DisplayOptions(
                        show={
                            "operation": ["aggregate"]
                        }
                    )
                ),
                # Document data (for insert operations)
                NodeParameter(
                    name="document",
                    display_name="Document (JSON Format)",
                    description="Document to insert in JSON format",
                    type=PropertyTypes.JSON,
                    required=True,
                    default="{}",
                    placeholder='{"name": "John", "email": "<EMAIL>", "age": 30}',
                    display_options=DisplayOptions(
                        show={
                            "operation": ["insert"]
                        }
                    )
                ),
                # Update data (for update operations)
                NodeParameter(
                    name="update",
                    display_name="Update (JSON Format)",
                    description="Update operations in JSON format",
                    type=PropertyTypes.JSON,
                    required=True,
                    default="{}",
                    placeholder='{"$set": {"status": "updated"}, "$inc": {"views": 1}}',
                    display_options=DisplayOptions(
                        show={
                            "operation": ["update", "findOneAndUpdate"]
                        }
                    )
                ),
                # Replacement document (for replace operations)
                NodeParameter(
                    name="replacement",
                    display_name="Replacement Document (JSON Format)",
                    description="Replacement document in JSON format",
                    type=PropertyTypes.JSON,
                    required=True,
                    default="{}",
                    placeholder='{"name": "Jane", "email": "<EMAIL>", "age": 25}',
                    display_options=DisplayOptions(
                        show={
                            "operation": ["findOneAndReplace"]
                        }
                    )
                ),
                # Options for various operations
                NodeParameter(
                    name="options",
                    display_name="Options",
                    description="Additional options for the operation",
                    type=PropertyTypes.JSON,
                    required=False,
                    default="{}",
                    placeholder='{"limit": 10, "sort": {"name": 1}, "projection": {"name": 1, "email": 1}}',
                    display_options=DisplayOptions(
                        show={
                            "operation": ["find", "update", "delete", "findOneAndUpdate", "findOneAndReplace"]
                        }
                    )
                ),
                # Upsert option (for update operations)
                NodeParameter(
                    name="upsert",
                    display_name="Upsert",
                    description="Create document if it doesn't exist",
                    type=PropertyTypes.BOOLEAN,
                    required=False,
                    default=False,
                    display_options=DisplayOptions(
                        show={
                            "operation": ["update", "findOneAndUpdate", "findOneAndReplace"]
                        }
                    )
                ),
                # Multi option (for update/delete operations)
                NodeParameter(
                    name="multi",
                    display_name="Update Multiple",
                    description="Update multiple documents that match the query",
                    type=PropertyTypes.BOOLEAN,
                    required=False,
                    default=False,
                    display_options=DisplayOptions(
                        show={
                            "operation": ["update", "delete"]
                        }
                    )
                ),
                # Timeout option
                NodeParameter(
                    name="timeout",
                    display_name="Timeout (seconds)",
                    description="Operation timeout in seconds",
                    type=PropertyTypes.NUMBER,
                    required=False,
                    default=30,
                    placeholder="30"
                )
            ]
        )
    
    def __init__(self):
        """Initialize DataStore node with operation handlers."""
        super().__init__()
        # Map operations to their corresponding handler classes
        self.operation_handlers: Dict[str, Callable] = {
            'find': FindOperation.execute,
            'insert': InsertOperation.execute,
            'update': UpdateOperation.execute,
            'delete': DeleteOperation.execute,
            'aggregate': AggregateOperation.execute,
            'findOneAndUpdate': FindAndModifyOperation.execute,
            'findOneAndReplace': FindAndModifyOperation.execute,
            'count': CountOperation.execute
        }

    
    def _validate_operation_parameters(self, operation: str, parameters: Dict[str, Any]) -> list:
        """
        Validate operation-specific parameters.
        
        Args:
            operation: The operation type
            parameters: Request parameters
            
        Returns:
            List of validation errors
        """
        from app.node.node_base.node_models import ValidationError
        
        errors = []
        
        try:
            if operation == 'insert':
                # Insert operations require documents
                if not parameters.get('documents') and not parameters.get('document'):
                    errors.append(ValidationError(
                        parameter="documents",
                        message="Documents or document is required for insert operation"
                    ))
            
            elif operation in ['update', 'findOneAndUpdate']:
                # Update operations require filter and update data
                if not parameters.get('query'):
                    errors.append(ValidationError(
                        parameter="query",
                        message="Filter is required for update operations"
                    ))
                if not parameters.get('update'):
                    errors.append(ValidationError(
                        parameter="update",
                        message="Update data is required for update operations"
                    ))
            
            elif operation == 'delete':
                # Delete operations require filter
                if not parameters.get('query'):
                    errors.append(ValidationError(
                        parameter="query",
                        message="Filter is required for delete operation"
                    ))
            
            elif operation == 'aggregate':
                # Aggregation operations require pipeline
                if not parameters.get('pipeline'):
                    errors.append(ValidationError(
                        parameter="pipeline",
                        message="Pipeline is required for aggregate operation"
                    ))
            
            elif operation == 'findOneAndReplace':
                # Replace operations require filter and replacement document
                if not parameters.get('query'):
                    errors.append(ValidationError(
                        parameter="query",
                        message="Filter is required for findOneAndReplace operation"
                    ))
                if not parameters.get('replacement'):
                    errors.append(ValidationError(
                        parameter="replacement",
                        message="Replacement document is required for findOneAndReplace operation"
                    ))
            
        except Exception as e:
            errors.append(ValidationError(
                parameter="operation_validation",
                message=f"Operation validation failed: {str(e)}"
            ))
        
        return errors

    async def run(self, data: NodeData) -> NodeResult:
        """
        Run the datastore node based on the specified operation.
        Args:
            request: Node request containing operation parameters
        
        Returns:
            NodeResult: Operation results or error information
        """
        try:
            operation = data.parameters.get('operation')
            if not operation:
                return NodeResult(error="Operation type is required")
            collection = str(data.parameters.get('collection'))
            if not collection:
                return NodeResult(error="Schema name is required")
            
            # Validate operation type
            if operation not in self.operation_handlers:
                available_operations = ', '.join(self.operation_handlers.keys())
                return NodeResult(
                    error=f"Unsupported operation: {operation}. "
                          f"Available operations: {available_operations}"
                )
            
            if operation == 'insert':
                # Check if document parameter exists and is a valid type
                if 'document' not in data.parameters:
                    return NodeResult(error="Document parameter is required for insert operation")
                document = data.parameters['document']
                if not isinstance(document, (dict, list)):
                    return NodeResult(error="Document must be a dictionary or list of dictionaries")
    
                # Create metadata fields to add to each document
                now = datetime.now()  # Only call datetime.now() once
                metadata = {
                    '_created_by': "admin", # TODO:  NKKK - Get the Workflow_id
                    '_created_by_source': "workflow",
                    '_created_on': now,
                    '_updated_by': "admin", # TODO:  NKKK - Get the Workflow_id
                    '_updated_by_source': "workflow",
                    '_updated_on': now
                }

                # Process bulk insert or single document insert
                if isinstance(document, list):
                    # Add metadata to each document in the list
                    for doc in document:
                        doc.update(metadata)
                else:
                    # Add metadata to the single document
                    data.parameters['document'] = {**document, **metadata}
                
            elif operation in ['update', 'findOneAndUpdate']:
                # Check if update parameter exists
                if 'update' not in data.parameters:
                    return NodeResult(error="Update parameter is required for update operation")
                
                update_data = data.parameters['update']
                if not isinstance(update_data, dict):
                    return NodeResult(error="Update must be a dictionary")
                
                # Add metadata for tracking update
                now = datetime.now()
                update_metadata = {
                    '_updated_by': "admin", # TODO:  NKKK - Get the Workflow_id
                    '_updated_by_source': "workflow",
                    '_updated_on': now
                }
                
                # Parse the update if it's a string (JSON)
                if isinstance(update_data, str):
                    try:
                        update_data = json.loads(update_data)
                    except json.JSONDecodeError:
                        return NodeResult(error="Invalid JSON in update parameter")
    
                # Check if the update contains valid MongoDB operators
                # If not, wrap it in $set
                if not any(key.startswith('$') for key in update_data.keys()):
                    update_data = {'$set': update_data}
                
                # Add metadata to $set operator
                if '$set' in update_data:
                    if not isinstance(update_data['$set'], dict):
                        return NodeResult(error="$set operator must contain a dictionary")
                    update_data['$set'].update(update_metadata)
                else:
                    # Create a $set operator with metadata
                    update_data['$set'] = update_metadata
                
                # Update the parameters
                data.parameters['update'] = update_data
            
            elif operation == 'findOneAndReplace':
                # Check if replacement parameter exists
                if 'replacement' not in data.parameters:
                    return NodeResult(error="Replacement document is required for findOneAndReplace operation")
                
                replacement_doc = data.parameters['replacement']
                if not isinstance(replacement_doc, dict):
                    return NodeResult(error="Replacement document must be a dictionary")
                
                # Create metadata fields to preserve history
                now = datetime.now()
                metadata = {
                    # Always update the updated fields
                    '_updated_by': "admin", # TODO:  NKKK - Get the Workflow_id
                    '_updated_by_source': "workflow",
                    '_updated_on': now
                }
                if data.parameters.get('upsert'):
                    # If upserting, also set the created fields
                    metadata.update({
                        '_created_by': "admin", # TODO:  NKKK - Get the Workflow_id
                        '_created_by_source': "workflow",
                        '_created_on': now
                    })
                
                # Add metadata to the replacement document
                data.parameters['replacement'] = {**replacement_doc, **metadata}
                
                # For findOneAndReplace, we need to ensure returnDocument is set to 'after'
                # to get the updated document in the result
                if 'options' not in data.parameters:
                    data.parameters['options'] = {}
    

            db = await get_mongodb()
            handler = self.operation_handlers[str(operation)]
            result = await handler(data, db)
            if result.error:
                print(f"DataStore {str(operation).upper()} operation failed: {result.error}")
            else:
                print(f"DataStore {str(operation).upper()} operation completed successfully")
            
            return result
        except Exception as e:
            error_message = f"DataStore node execution failed: {str(e)}"
            print(error_message)
            return NodeResult(error=error_message)
    
    def validate(self, data: NodeRequest) -> ValidationResult:
        """
            Validate the datastore node request parameters.
            
            Args:
                request (NodeRequest): The request containing parameters to validate
                
            Returns:
                ValidationResult: Result containing validation status and any errors
        """
        try:
            errors = []
            # Check if operation is provided
            operation = data.parameters.get('operation')
            if not operation:
                errors.append(ValidationError(
                    parameter="operation",
                    message="Operation type is required"
                ))
            else:
                # Validate operation type
                if operation not in self.operation_handlers:
                    available_operations = ', '.join(self.operation_handlers.keys())
                    errors.append(ValidationError(
                        parameter="operation",
                        message=f"Unsupported operation: {operation}. Available operations: {available_operations}"
                    ))
            
            # Check for collection name (required for most operations)
            if operation and operation in ['find', 'insert', 'update', 'delete', 'count', 'findOneAndUpdate', 'findOneAndReplace']:
                if not data.parameters.get('collection'):
                    errors.append(ValidationError(
                        parameter="collection",
                        message="Collection name is required for this operation"
                    ))
            
            # Operation-specific validation
            if operation and not errors:
                if isinstance(operation, str):
                    operation_errors = self._validate_operation_parameters(operation, data.parameters)
                    errors.extend(operation_errors)
                else:
                    errors.append(ValidationError(
                        parameter="operation",
                        message=f"Operation type must be a string, got {type(operation).__name__}"
                    ))
            
            # Return validation result
            return ValidationResult(
                valid=len(errors) == 0,
                errors=errors if errors else None
            )
        except Exception as e:
            error_message = f"DataStore node validation failed: {str(e)}"
            print(error_message)
            return ValidationResult(
                valid=False,
                errors=[ValidationError(
                    parameter="validation",
                    message=error_message
                )]
            )
    
    async def get_datastore_collections(self, request: NodeData) -> list[dict]:
        """
        Load available MongoDB collections for dropdown selection.
        """
        try:
            from app.core.database import get_mongodb
            db = await get_mongodb()
            collections = await db.list_collection_names()
            return [{"name": name, "value": name} for name in collections]
        except Exception as e:
            print(f"Failed to load MongoDB collections: {str(e)}")
            return []
