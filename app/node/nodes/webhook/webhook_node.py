"""
Webhook Node Implementation

This module implements the webhook trigger node for receiving HTTP requests
and starting workflows based on incoming webhook data.
"""

import json
import logging
import base64
from typing import Dict, Any, Optional
from urllib.parse import parse_qs
from temporalio import workflow
from datetime import datetime, timezone


from app.node.node_base.node import Node, NodeR<PERSON>ult
from app.node.node_base.node_models import NodeData, NodeTypeDescription, ValidationResult
from app.node.nodes.webhook.webhook_model import WebhookNodeDescription
from app.node.node_utils.workflow_defn import node_defn

with workflow.unsafe.imports_passed_through():
    from fastapi import Request, HTTPException, status
    from app.credential.utils.credential_manager import CredentialManager


# Set up logging
logger = logging.getLogger(__name__)


@node_defn(type='webhook', is_activity=True)
class WebhookNode(Node):
    """
    Webhook Trigger Node
    
    Provides webhook functionality for receiving HTTP requests and triggering
    workflows based on incoming data. Supports various authentication methods,
    response modes, and data processing options.
    """

    allow_multiple_trigger_values = False
    
    @classmethod
    def get_description(cls) -> NodeTypeDescription:
        """Get the node type description."""
        return WebhookNodeDescription.create()
    
    def __init__(self):
        super().__init__()
        self.webhook_registry = {}  # Store active webhooks
    
    def _validate_ip_whitelist(self, ip_whitelist: str, client_ip: str) -> bool:
        """
        Validate if the client IP is in the whitelist.
        
        Args:
            ip_whitelist: Comma-separated list of allowed IPs
            client_ip: Client's IP address
            
        Returns:
            bool: True if IP is allowed, False otherwise
        """
        if not ip_whitelist or ip_whitelist.strip() == "":
            return True  # No whitelist means all IPs are allowed
        
        allowed_ips = [ip.strip() for ip in ip_whitelist.split(",")]
        return client_ip in allowed_ips
    
    def _is_bot_request(self, user_agent: str) -> bool:
        """
        Check if the request is from a bot or crawler.
        
        Args:
            user_agent: User-Agent header value
            
        Returns:
            bool: True if request is from a bot
        """
        if not user_agent:
            return False
        
        bot_indicators = [
            'bot', 'crawler', 'spider', 'scraper', 'curl', 'wget',
            'python-requests', 'postman', 'insomnia', 'facebookexternalhit',
            'twitterbot', 'linkedinbot', 'whatsapp', 'telegram'
        ]
        
        user_agent_lower = user_agent.lower()
        return any(indicator in user_agent_lower for indicator in bot_indicators)
    
    def _parse_request_data(self, request_data: Dict[str, Any], options: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse and structure the incoming request data.
        
        Args:
            request_data: Raw request data
            options: Webhook options
            
        Returns:
            Dict containing structured request data
        """
        parsed_data = {
            "headers": request_data.get("headers", {}),
            "query": request_data.get("query", {}),
            "params": request_data.get("params", {}),
            "method": request_data.get("method", "GET"),
            "url": request_data.get("url", ""),
            "timestamp": request_data.get("timestamp"),
            "body": {}
        }
        
        # Parse body based on content type
        body = request_data.get("body")
        content_type = request_data.get("headers", {}).get("content-type", "")
        
        if body:
            if "application/json" in content_type:
                try:
                    parsed_data["body"] = json.loads(body) if isinstance(body, str) else body
                except json.JSONDecodeError:
                    parsed_data["body"] = {"raw": body}
            elif "application/x-www-form-urlencoded" in content_type:
                if isinstance(body, str):
                    parsed_data["body"] = dict(parse_qs(body, keep_blank_values=True))
                else:
                    parsed_data["body"] = body
            elif "multipart/form-data" in content_type:
                # Handle multipart form data
                parsed_data["body"] = body
                if options.get("binary_data"):
                    parsed_data["files"] = request_data.get("files", {})
            else:
                parsed_data["body"] = {"raw": body}
        
        # Include raw body if requested
        if options.get("raw_body"):
            if isinstance(body, str):
                parsed_data["raw_body"] = body
            elif body:
                parsed_data["raw_body"] = base64.b64encode(
                    body if isinstance(body, bytes) else str(body).encode()
                ).decode()
        
        return parsed_data
    
    def _create_response(self, data: NodeData, webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create the webhook response based on configuration.
        
        Args:
            data: Node configuration data
            webhook_data: Processed webhook data
            
        Returns:
            Dict containing response configuration
        """
        response_mode = data.parameters.get("response_mode", "immediately")
        response_code = data.parameters.get("response_code", 200)
        response_data = data.parameters.get("response_data", {"message": "Webhook received successfully"})
        response_headers = data.parameters.get("response_headers", {})
        
        # Convert headers list to dict if needed
        headers_dict = {}
        if isinstance(response_headers, list):
            for header in response_headers:
                if isinstance(header, dict) and "name" in header and "value" in header:
                    headers_dict[header["name"]] = header["value"]
        elif isinstance(response_headers, dict):
            headers_dict = response_headers
        
        # Add default headers
        headers_dict.setdefault("Content-Type", "application/json")
        
        response = {
            "status_code": response_code,
            "headers": headers_dict,
            "data": response_data
        }
        
        if response_mode == "immediately":
            response["immediate"] = True
        elif response_mode == "on_completion":
            response["immediate"] = False
            response["data"] = webhook_data  # Return the processed webhook data
        
        return response
    
    def get_client_ip(self, request: Request) -> str:
        """
        Extract client IP address from request.
        
        Args:
            request: FastAPI request object
            
        Returns:
            str: Client IP address
        """
        # Check for forwarded headers first (for reverse proxies)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fall back to direct connection IP
        return request.client.host if request.client else "unknown"

    def parse_request_body(self,request: Request, body: bytes) -> Dict[str, Any]:
        """
        Parse request body based on content type.
        
        Args:
            request: FastAPI request object
            body: Raw request body
            
        Returns:
            Dict containing parsed body data
        """
        content_type = request.headers.get("content-type", "").lower()
        
        try:
            if "application/json" in content_type:
                return json.loads(body.decode("utf-8"))
            elif "application/x-www-form-urlencoded" in content_type:
                from urllib.parse import parse_qs
                body_str = body.decode("utf-8")
                return dict(parse_qs(body_str, keep_blank_values=True))
            elif "text/" in content_type:
                return {"text": body.decode("utf-8")}
            else:
                # For binary data, return base64 encoded
                return {"binary": base64.b64encode(body).decode("utf-8")}
        except Exception as e:
            logger.warning(f"Failed to parse request body: {str(e)}")
            return {"raw": body.decode("utf-8", errors="ignore")}

    async def run(self, data: NodeData) -> NodeResult:
        """
        Execute the webhook node (this is called when the webhook is triggered).

        Args:
            data: Node execution data containing webhook request information

        Returns:
            NodeResult with the processed webhook data
        """
        # For webhook nodes, the actual processing happens in process_webhook
        # This method is called when the webhook is triggered

        webhook_config = data.parameters
        request = data.input_data
        webhook_path = webhook_config.get("webhook_path")
        options = webhook_config.get("options", {})
        logger.info(f"Webhook node executed for path: {webhook_path}")

        workflow_id = data.parameters.get("workflow_id")

        # Validate HTTP method
        allowed_method = webhook_config.get("http_methods", "GET")
        if not request or not hasattr(request, "method") or request.method != allowed_method:
            method = getattr(request, "method", None)
            logger.warning(f"Method not allowed for webhook {webhook_path}: {method}")
            raise HTTPException(
                status_code=status.HTTP_405_METHOD_NOT_ALLOWED,
                detail=f"Method {method} not allowed"
            )

         # Validate IP whitelist
        client_ip = str(webhook_config.get("client_ip", ""))
        ip_whitelist = options.get("ip_whitelist", "") if isinstance(options, dict) else ""
        if not self._validate_ip_whitelist(ip_whitelist, client_ip):
            logger.warning(f"Webhook request blocked: IP {client_ip} not in whitelist")
            return NodeResult(
                error="IP address not allowed"
            )
            
        # Check for bot requests if configured
        if isinstance(options, dict) and options.get("ignore_bots", False):
            headers = webhook_config.get("headers", {})
            if not isinstance(headers, dict):
                headers = {}
            user_agent = headers.get("user-agent", "")
            if self._is_bot_request(user_agent):
                logger.info("Webhook request ignored: Bot detected")
                return NodeResult(
                    error="Bot request ignored"
                )

        # Authenticate request if required
        if webhook_config.get("webhook_authentication_type", "none") != "none":
            credential = None
            if webhook_config.get("webhook_authentication_type") == "basic":
                if data.credentials is not None:
                    credential = data.credentials.get("basic_auth", "")
            elif webhook_config.get("webhook_authentication_type") == "header":
                if data.credentials is not None:
                    credential = data.credentials.get("header_auth", "")
            elif webhook_config.get("webhook_authentication_type") == "jwt":
                if data.credentials is not None:
                    credential = data.credentials.get("jwt_auth", "")

            credential_id = None
            if isinstance(credential, dict) and "id" in credential:
                credential_id = credential.get("id")
            elif hasattr(credential, "id"):
                credential_id = getattr(credential, "id")
            elif isinstance(credential, (str, int, float)):
                credential_id = str(credential)

            if credential_id and not await CredentialManager().verify_request_authentication(str(credential_id), request.headers, "webhook"):
                logger.warning(f"Authentication failed for webhook: {webhook_path}")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication failed"
                )
        
        # Read request body
        body = await request.body()
        # Prepare webhook data
        webhook_data = {
            "webhook_path": webhook_path,
            "method": request.method,
            "headers": dict(request.headers),
            "query": dict(request.query_params),
            "body": self.parse_request_body(request, body),
            "client_ip": self.get_client_ip(request),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
            
        logger.info(
            f"Webhook triggered workflow execution: webhook_path={webhook_path}, client_ip={webhook_data['client_ip']}, workflow_id={workflow_id}"
        )

        # Return the webhook data that was passed to this node
        return NodeResult(
            result=webhook_data,
            next_connection_index=0
        )

    def validate(self, request: NodeData) -> ValidationResult:
        """
        Validate a webhook node request with comprehensive checks.

        Args:
            request: The node request to validate

        Returns:
            ValidationResult with any validation errors
        """

        # Add webhook-specific validation
        errors = []

        # Validate webhook path
        webhook_path = request.parameters.get("webhook_path")
        if not webhook_path:
            errors.append({
                "parameter": "webhook_path",
                "message": "Webhook path is required"
            })
        elif not isinstance(webhook_path, str) or not webhook_path.replace("-", "").replace("_", "").isalnum():
            errors.append({
                "parameter": "webhook_path",
                "message": "Webhook path must contain only letters, numbers, hyphens, and underscores"
            })

        # Validate HTTP methods
        http_methods = request.parameters.get("http_method", [])
        if not http_methods:
            errors.append({
                "parameter": "http_method",
                "message": "At least one HTTP method must be selected"
            })

        # Validate response code
        response_code = request.parameters.get("response_code")
        if response_code is not None and isinstance(response_code, (int, float, str)):
            try:
                code = int(response_code)
                if not 100 <= code <= 599:
                    errors.append({
                        "parameter": "response_code",
                        "message": "Response code must be between 100 and 599"
                    })
            except (ValueError, TypeError):
                errors.append({
                    "parameter": "response_code",
                    "message": "Response code must be a valid number"
                })

        # Validate IP whitelist format
        options = request.parameters.get("options", {})
        if isinstance(options, dict):
            ip_whitelist = options.get("ip_whitelist", "")
            if ip_whitelist:
                # Basic IP validation (simplified)
                ips = [ip.strip() for ip in ip_whitelist.split(",")]
                for ip in ips:
                    if ip and not self._is_valid_ip_format(ip):
                        errors.append({
                            "parameter": "options.ip_whitelist",
                            "message": f"Invalid IP address format: {ip}"
                        })

        return ValidationResult(
            valid=len(errors) == 0,
            errors=errors if errors else None
        )

    def _is_valid_ip_format(self, ip: str) -> bool:
        """
        Basic IP address format validation.

        Args:
            ip: IP address string to validate

        Returns:
            bool: True if IP format is valid
        """
        try:
            parts = ip.split(".")
            if len(parts) != 4:
                return False
            for part in parts:
                num = int(part)
                if not 0 <= num <= 255:
                    return False
            return True
        except (ValueError, AttributeError):
            return False
        
    def create_trigger_value(self, node: NodeData) -> str:
        """
        Get the trigger value for this node.
        
        Args:
            node: The node data
            
        Returns:
            str: The trigger value
        """
        return f'{node.parameters.get("http_method", "GET")}_{node.parameters.get("webhook_path", "")}'
    
    def get_trigger_value(self, data: Any) -> Optional[str]:
        """
        Get the trigger value for this node.
        
        Args:
            data: The node data
            
        Returns:
            str: The trigger value
        """
        request = data.input_data
        http_method = request.get("method")
        webhook_path = data.trigger_value
        if not http_method or not webhook_path:
            return ""
        
        return f'{http_method}_{webhook_path}'
