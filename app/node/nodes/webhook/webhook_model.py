"""
Webhook Node Model

This module defines the webhook node model with parameter definitions,
credentials, and configuration following the project's established patterns.
"""

import secrets
import string
from typing import ClassVar
from app.node import (
    NodeConnectionType,
    NodeCredentialDescription,
    NodeParameter,
    NodeParameterOption,
    PropertyTypes,
    NodeTypeDescription,
    DisplayOptions
)
from app.node.node_base.node_models import PropertyTypeOptions


def generate_webhook_path() -> str:
    """
    Generate a random webhook path for security.
    
    Returns:
        str: A random string suitable for webhook paths
    """
    # Generate a random string of 16 characters (letters and numbers)
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(16))


class WebhookNodeDescription(NodeTypeDescription):
    """
    Description for the Webhook Trigger Node.
    
    This node provides webhook functionality for receiving HTTP requests
    and triggering workflows based on incoming data.
    """
    
    # Class variable to define the node type
    TYPE: ClassVar[str] = "webhook"
    
    @classmethod
    def create(cls) -> "WebhookNodeDescription":
        """
        Factory method to create a standard webhook node description.
        
        Returns:
            WebhookNodeDescription: A configured description for webhook nodes
        """
        return cls(
            name="webhook",
            display_name="Webhook",
            description="Starts the workflow when a webhook is called",
            icon="🔗",
            icon_color="#4A90E2",
            group=["trigger"],
            version=1.0,
            inputs=[],  # Trigger nodes have no inputs
            outputs=[NodeConnectionType.Main],

            parameters=[
                NodeParameter(
                    name="webhook_path",
                    display_name="Webhook Path",
                    description="The path for the webhook endpoint (leave empty for auto-generated)",
                    type=PropertyTypes.STRING,
                    default=f"webhook-{generate_webhook_path()}",
                    required=True,
                    placeholder="webhook-abc123def456"
                ),

                NodeParameter(
                    name="webhook_authentication_type",
                    display_name="Authentication Type",
                    description="Configure authentication for the webhook",
                    type=PropertyTypes.OPTIONS,
                    default="none",
                    required=True,
                    options=[
                        NodeParameterOption(name="None", value="none"),
                        NodeParameterOption(name="Basic Auth", value="basic"),
                        NodeParameterOption(name="Header Auth", value="header"),
                        NodeParameterOption(name="JWT Auth", value="jwt")
                    ]
                ),

                NodeParameter(
                    name="basic_auth",
                    display_name="Basic Auth",
                    description="Configure basic authentication",
                    type=PropertyTypes.CREDENTIALS_SELECT,
                    credential_type="basic_auth",
                    default="",
                    required=False,
                    display_options=DisplayOptions(
                        show={"webhook_authentication_type": ["basic"]}
                    )
                ),

                NodeParameter(
                    name="header_auth",
                    display_name="Header Auth",
                    description="Configure header authentication",
                    type=PropertyTypes.CREDENTIALS_SELECT,
                    credential_type="header_auth",
                    default="",
                    required=False,
                    display_options=DisplayOptions(
                        show={"webhook_authentication_type": ["header"]}
                    )
                ),

                NodeParameter(
                    name="jwt_auth",
                    display_name="JWT Auth",
                    description="Configure JWT authentication",
                    type=PropertyTypes.CREDENTIALS_SELECT,
                    credential_type="jwt_auth",
                    default="",
                    required=False,
                    display_options=DisplayOptions(
                        show={"webhook_authentication_type": ["jwt"]}
                    )
                ),
                
                NodeParameter(
                    name="http_method",
                    display_name="HTTP Method",
                    description="The HTTP method(s) to listen for",
                    type=PropertyTypes.OPTIONS,
                    default="GET",
                    required=True,
                    options=[
                        NodeParameterOption(name="GET", value="GET"),
                        NodeParameterOption(name="POST", value="POST"),
                        NodeParameterOption(name="PUT", value="PUT"),
                        NodeParameterOption(name="PATCH", value="PATCH"),
                        NodeParameterOption(name="DELETE", value="DELETE"),
                        NodeParameterOption(name="HEAD", value="HEAD"),
                        NodeParameterOption(name="OPTIONS", value="OPTIONS")
                    ]
                ),
                
                NodeParameter(
                    name="response_mode",
                    display_name="Response Mode",
                    description="When and how to respond to the webhook",
                    type=PropertyTypes.OPTIONS,
                    default="immediately",
                    required=True,
                    options=[
                        NodeParameterOption(
                            name="Immediately",
                            value="immediately",
                            description="Respond as soon as the webhook is received"
                        ),
                        NodeParameterOption(
                            name="When Workflow Completes",
                            value="on_completion",
                            description="Respond when the entire workflow finishes"
                        ),
                        NodeParameterOption(
                            name="Custom Response",
                            value="custom",
                            description="Use custom response data"
                        )
                    ]
                ),
                
                NodeParameter(
                    name="response_code",
                    display_name="Response Code",
                    description="HTTP status code to return",
                    type=PropertyTypes.NUMBER,
                    default=200,
                    required=True,
                    type_options=PropertyTypeOptions(
                        min_value=100,
                        max_value=599
                    ),
                    display_options=DisplayOptions(
                        show={"response_mode": ["immediately", "custom"]}
                    )
                ),
                
                NodeParameter(
                    name="response_data",
                    display_name="Response Data",
                    description="Custom response data to return (JSON format)",
                    type=PropertyTypes.JSON,
                    default={"message": "Webhook received successfully"},
                    required=False,
                    display_options=DisplayOptions(
                        show={"response_mode": ["custom"]}
                    )
                ),
                
                NodeParameter(
                    name="response_headers",
                    display_name="Response Headers",
                    description="Custom headers to include in the response",
                    type=PropertyTypes.COLLECTION,
                    default={},
                    required=False,
                    type_options=PropertyTypeOptions(
                        multiple_values=True
                    ),
                    display_options=DisplayOptions(
                        show={"response_mode": ["immediately", "custom"]}
                    )
                ),
                
                NodeParameter(
                    name="options",
                    display_name="Options",
                    description="Additional webhook options",
                    type=PropertyTypes.COLLECTION,
                    default={},
                    required=False
                )
            ]
        )
