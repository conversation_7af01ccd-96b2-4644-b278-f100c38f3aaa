"""
WhatsApp Node Implementation

This module implements the WhatsApp Business Cloud node for sending messages,
media, and templates via the WhatsApp Business API.
"""

import re
import logging
from typing import Dict, Any, List, Optional

from app.node.node_base.node import Node, NodeResult
from app.node.node_base.node_models import NodeData, NodeParameter, PropertyTypeOptions, PropertyTypes, NodeRequest, NodeTypeDescription, StyleOptions, ValidationResult
from app.node.nodes.whatsapp.whatsapp_model import WhatsAppNodeDescription
from app.node.node_utils.workflow_defn import node_defn


# Set up logging
logger = logging.getLogger(__name__)


@node_defn(type='whatsapp', is_activity=True)
class WhatsAppNode(Node):
    """
    WhatsApp Business Cloud Node
    
    Provides integration with WhatsApp Business Cloud API for sending
    messages, media, and templates to WhatsApp users.
    """
    
    # WhatsApp API base URL
    WHATSAPP_BASE_URL = "https://graph.facebook.com/v18.0"
    
    @classmethod
    def get_description(cls) -> NodeTypeDescription:
        """Get the node type description."""
        return WhatsAppNodeDescription.create()
    
    def __init__(self):
        super().__init__()
        # HTTP session will be initialized lazily when needed
        self.session = None

    async def get_phone_numbers(self, request: NodeRequest) -> List[Dict[str, Any]]:
        """Get list of phone numbers for the given credentials."""
        from app.utils.http_client import HttpClient, HttpMethod
        if not request.credentials or 'whatsapp_api' not in request.credentials:
            return []
        
        url = f"{self.WHATSAPP_BASE_URL}/{{credential.business_id}}/phone_numbers"
        response = await HttpClient.request(
            method=HttpMethod.GET,
            url=url,
            credential=request.credentials['whatsapp_api'].id,
            node_type='whatsapp'
        )
        data = [
        {
            'name': f"{x['display_phone_number']} ({x['verified_name']})",
            'value': x['id']
        }
            for x in response["json"]["data"]
        ]
        return data

    async def get_all_templates(self, request: NodeRequest) -> List[Dict[str, Any]]:
        """Get all message templates for the given credentials."""
        from app.utils.http_client import HttpClient, HttpMethod
        if not request.credentials or 'whatsapp_api' not in request.credentials:
            return []
        url = f"{self.WHATSAPP_BASE_URL}/{{credential.business_id}}/message_templates"
        response = await HttpClient.request(
            method=HttpMethod.GET,
            url=url,
            credential=request.credentials['whatsapp_api'].id,
            node_type='whatsapp'
        )
        data = [
            {
                'name': f"{x['name']} ({x['language']})",
                'value': x['id']
            }
            for x in response["json"]["data"]
        ]
        return data
    
    async def get_template(self, request: NodeRequest) -> List[Dict[str, Any]]:
        """Get message templates for the given credentials and name."""
        from app.utils.http_client import HttpClient, HttpMethod
        if not request.credentials or 'whatsapp_api' not in request.credentials:
            return []
        template_id = request.parameters["template"]
        url = f"{self.WHATSAPP_BASE_URL}/{template_id}"
        response = await HttpClient.request(
            method=HttpMethod.GET,
            url=url,
            credential=request.credentials['whatsapp_api'].id,
            node_type='whatsapp'
        )
        return [component.model_dump(exclude_none=True) for component in self._create_template_components(response["json"])]
    
    
    def _create_template_components(self, template: Dict[str, Any]) -> List[NodeParameter]:
        components = template.get('components', [])
        if not components:
            return []
        ui_components = []
        for component in components:
            component_type = component.get('type')
            if component_type == 'HEADER':
                ui_components.append(
                    NodeParameter(
                    name='header',
                    display_name='Header',
                    type=PropertyTypes.TEXT_VIEW,
                    type_options=PropertyTypeOptions(
                        style=StyleOptions(
                            font_weight="bold",
                            font_size="1.2em"
                        )
                    ),
                ))
                if component.get("format") == 'TEXT':
                    ui_components.append(
                        NodeParameter(
                            name='headerText',
                            display_name=component['text'],
                            type=PropertyTypes.STRING,
                            type_options=PropertyTypeOptions(
                                editor_is_read_only=True
                            ),
                        )
                    )
                    if 'example' in component and isinstance(component['example'], dict):
                        ui_components.append(
                            NodeParameter(
                            name='variables',
                            display_name='Variables',
                            description='Variables for the header',
                            type=PropertyTypes.TEXT_VIEW,
                            type_options=PropertyTypeOptions(
                                style=StyleOptions(
                                    font_weight="700",
                                )
                            ),
                        ))
                        if 'header_text' in component['example']:
                            ui_components.extend(self._create_template_variables(component['example']['header_text'], 'header_text'))
                        if 'header_text_named_params' in component['example']:
                            ui_components.extend(self._create_template_variables(component['example']['header_text_named_params'], 'header_text_named_params'))
            elif component_type == 'BODY':
                ui_components.append(
                    NodeParameter(
                    name='body',
                    display_name='Body',
                    type=PropertyTypes.TEXT_VIEW,
                    type_options=PropertyTypeOptions(
                        style=StyleOptions(
                            font_weight="bold",
                            font_size="1.2em"
                        )
                    ),
                ))
                
                ui_components.append(
                    NodeParameter(
                        name='bodyText',
                        display_name=component['text'],
                        type=PropertyTypes.STRING,
                        type_options=PropertyTypeOptions(
                            editor_is_read_only=True
                        ),
                    )
                )
                if 'example' in component and isinstance(component['example'], dict):
                    ui_components.append(
                        NodeParameter(
                        name='variables',
                        display_name='Variables',
                        description='Variables for the body',
                        type=PropertyTypes.TEXT_VIEW,
                        type_options=PropertyTypeOptions(
                            style=StyleOptions(
                                font_weight="700",
                            )
                        ),
                    ))
                    if 'body_text' in component['example']:
                        ui_components.extend(self._create_template_variables(component['example']['body_text'], 'body_text'))
                    if 'body_text_named_params' in component['example']:
                        ui_components.extend(self._create_template_variables(component['example']['body_text_named_params'], 'body_text_named_params'))

            elif component_type == 'BUTTONS':
                for button in component.get('buttons', []):
                    if button.get('type') == 'URL':
                        ui_components.append(
                            NodeParameter(
                            name='button',
                            display_name=button['text'],
                            type=PropertyTypes.STRING,
                            type_options=PropertyTypeOptions(
                                editor_is_read_only=True
                            ),
                        ))
                        ui_components.append(
                            NodeParameter(
                            name='url',
                            display_name='URL',
                            type=PropertyTypes.STRING,
                        ))
                        if 'example' in component and isinstance(component['example'], dict):
                            ui_components.append(
                                NodeParameter(
                                name='variables',
                                display_name='Variables',
                                description='Variables for the button',
                                type=PropertyTypes.TEXT_VIEW,
                                type_options=PropertyTypeOptions(
                                    style=StyleOptions(
                                        font_weight="700",
                                    )
                                ),
                            ))
                            ui_components.extend(self._create_template_variables(component['example'], 'button_url'))

        return ui_components

    def _create_template_variables(self, templates: List[Any], template_name: str) -> List[NodeParameter]:
        if not templates:
            return []
        variables = []
        if isinstance(templates[0], dict):
            for params in templates:
                variables.append(
                    NodeParameter(
                        name=params['param_name'],
                        display_name=params['param_name'],
                        placeholder=params['example'],
                        type=PropertyTypes.STRING,
                        required=True,
                        type_options=PropertyTypeOptions(
                            editor_is_read_only=True
                        ),
                    )
                )
        elif isinstance(templates[0], list):
            for i,params in enumerate(templates):
                variables.extend(self._create_template_variables(params, f'{template_name}_{i}'))
        else:
            for i in range(len(templates)):
                variables.append(
                    NodeParameter(
                        name=f'{template_name}_{i}',
                        display_name=f'{i+1}',
                        placeholder=str(templates[i]),
                        required=True,
                        type=PropertyTypes.STRING,
                    )
                )
        return variables
    
    def _sanitize_phone_number(self, phone_number: str) -> str:
        """
        Sanitize phone number by removing special characters.
        
        Args:
            phone_number: Raw phone number string
            
        Returns:
            Sanitized phone number with only digits
        """
        return re.sub(r'[\-\(\)\+\s]', '', phone_number)

    async def _make_api_request(self, method: str, endpoint: str, credential: str, data: Optional[Dict[str, Any]] = None,
                         headers: Optional[Dict[str, str]] = None, ) -> Dict[str, Any]:
        """
        Make a request to the WhatsApp API with comprehensive error handling.

        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint
            data: Request payload
            headers: Request headers

        Returns:
            API response as dictionary

        Raises:
            Exception: If the API request fails
        """
        # Import requests locally to avoid Temporal sandbox issues
        import requests
        from app.utils.http_client import HttpClient

        url = f"{self.WHATSAPP_BASE_URL}/{endpoint}"
        # session = self._get_http_session()

        logger.info(f"Making {method} request to WhatsApp API: {endpoint}")

        try:
            # response = session.request(
            #     method=method,
            #     url=url,
            #     json=data,
            #     headers=headers,
            #     timeout=30
            # )

            response = await HttpClient.request(
                method=method,
                url=url,
                json_data=data,
                headers=headers,
                timeout=30,
                credential=credential,
                node_type='whatsapp'
            )

            status_code = response["status_code"]

            # Log response status
            logger.info(f"WhatsApp API response status: {status_code}")

            # Handle different HTTP status codes
            if status_code == 200:
                return response["json"]
            # elif status_code == 400:
            #     error_data = response.json() if response.content else {}
            #     error_msg = error_data.get('error', {}).get('message', 'Bad Request')
            #     logger.error(f"WhatsApp API bad request: {error_msg}")
            #     raise Exception(f"WhatsApp API error: {error_msg}")
            # elif response.status_code == 401:
            #     logger.error("WhatsApp API authentication failed")
            #     raise Exception("WhatsApp API authentication failed. Check your access token.")
            # elif response.status_code == 403:
            #     logger.error("WhatsApp API access forbidden")
            #     raise Exception("WhatsApp API access forbidden. Check your permissions.")
            # elif response.status_code == 429:
            #     logger.warning("WhatsApp API rate limit exceeded")
            #     raise Exception("WhatsApp API rate limit exceeded. Please try again later.")
            # elif response.status_code >= 500:
            #     logger.error(f"WhatsApp API server error: {response.status_code}")
            #     raise Exception(f"WhatsApp API server error: {response.status_code}")
            # else:
            #     response.raise_for_status()
            #     return response.json()
            return {}
        except requests.Timeout as e:
            logger.error(f"WhatsApp API request timeout: {str(e)}")
            raise Exception(f"WhatsApp API request timeout: {str(e)}")
        except requests.ConnectionError as e:
            logger.error(f"WhatsApp API connection error: {str(e)}")
            raise Exception(f"WhatsApp API connection error: {str(e)}")
        except requests.RequestException as e:
            logger.error(f"WhatsApp API request failed: {str(e)}")
            raise Exception(f"WhatsApp API request failed: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error in WhatsApp API request: {str(e)}")
            raise Exception(f"Unexpected error in WhatsApp API request: {str(e)}")
    
    async def _send_text_message(self, phone_number_id: str, recipient: str, 
                          message: str, credential: str) -> Dict[str, Any]:
        """
        Send a text message via WhatsApp API.
        
        Args:
            phone_number_id: WhatsApp Business phone number ID
            recipient: Recipient phone number
            message: Text message content
            access_token: WhatsApp API access token
            
        Returns:
            API response
        """
        
        payload = {
            'messaging_product': 'whatsapp',
            'to': self._sanitize_phone_number(recipient),
            'type': 'text',
            'text': {
                'body': message
            }
        }
        
        return await self._make_api_request(
            'POST', 
            f"{phone_number_id}/messages", 
            credential,
            payload,
        )
    
    async def _send_media_message(self, phone_number_id: str, recipient: str, credential: str,
                           media_type: str, media_url: str,
                           caption: Optional[str] = None, filename: Optional[str] = None) -> Dict[str, Any]:
        """
        Send a media message via WhatsApp API.
        
        Args:
            phone_number_id: WhatsApp Business phone number ID
            recipient: Recipient phone number
            media_type: Type of media (image, document, audio, video, sticker)
            media_url: URL of the media file
            access_token: WhatsApp API access token
            caption: Optional caption for the media
            filename: Optional filename for documents
            
        Returns:
            API response
        """
        
        media_object = {'link': media_url}
        
        # Add caption if provided and supported
        if caption and media_type in ['image', 'document', 'video']:
            media_object['caption'] = caption
            
        # Add filename for documents
        if filename and media_type == 'document':
            media_object['filename'] = filename
        
        payload = {
            'messaging_product': 'whatsapp',
            'to': self._sanitize_phone_number(recipient),
            'type': media_type,
            media_type: media_object
        }
        
        return await self._make_api_request(
            'POST',
            f"{phone_number_id}/messages",
            credential,
            payload
        )

    async def _send_template_message(self, phone_number_id: str, recipient: str,
                              template: str, credential: str,
                              template_params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Send a template message via WhatsApp API.

        Args:
            phone_number_id: WhatsApp Business phone number ID
            recipient: Recipient phone number
            template: Template name and language (format: name|language)
            access_token: WhatsApp API access token
            template_params: Optional template parameters

        Returns:
            API response
        """

        # Parse template name and language
        try:
            template_name, language_code = template.split('|')
        except ValueError:
            raise ValueError("Template must be in format 'template_name|language_code'")

        template_obj = {
            'name': template_name,
            'language': {
                'code': language_code
            }
        }

        # Add template parameters if provided
        if template_params and template_params.get('parameter'):
            components = [{
                'type': 'body',
                'parameters': template_params['parameter']
            }]
            template_obj['components'] = components

        payload = {
            'messaging_product': 'whatsapp',
            'to': self._sanitize_phone_number(recipient),
            'type': 'template',
            'template': template_obj
        }

        return await self._make_api_request(
            'POST',
            f"{phone_number_id}/messages",
            credential,
            payload,
        )

    async def _send_location_message(self, phone_number_id: str, recipient: str,
                              latitude: float, longitude: float, credential: str,
                              name: Optional[str] = None, address: Optional[str] = None) -> Dict[str, Any]:
        """
        Send a location message via WhatsApp API.

        Args:
            phone_number_id: WhatsApp Business phone number ID
            recipient: Recipient phone number
            latitude: Latitude coordinate
            longitude: Longitude coordinate
            access_token: WhatsApp API access token
            name: Optional location name
            address: Optional location address

        Returns:
            API response
        """

        location_obj: Dict[str, Any] = {
            'latitude': latitude,
            'longitude': longitude
        }

        if name:
            location_obj['name'] = name
        if address:
            location_obj['address'] = address

        payload = {
            'messaging_product': 'whatsapp',
            'to': self._sanitize_phone_number(recipient),
            'type': 'location',
            'location': location_obj
        }

        return await self._make_api_request(
            'POST',
            f"{phone_number_id}/messages",
            credential,
            payload,
        )

    async def _upload_media(self, phone_number_id: str, media_file: str,
                     media_type: str, credential: str) -> Dict[str, Any]:
        """
        Upload media to WhatsApp API.

        Args:
            phone_number_id: WhatsApp Business phone number ID
            media_file: Media file path or base64 data
            media_type: Type of media
            access_token: WhatsApp API access token

        Returns:
            API response with media ID
        """

        # For now, we'll handle URL-based media
        # In a full implementation, this would handle file uploads
        files = {
            'messaging_product': (None, 'whatsapp'),
            'type': (None, media_type),
            'file': (None, media_file)  # This would be actual file data
        }

        from app.utils.http_client import HttpClient
        url = f"{self.WHATSAPP_BASE_URL}/{phone_number_id}/media"
        # session = self._get_http_session()

        try:
            response = await HttpClient.request(
                method='POST',
                url=url,
                data=files,
                credential=credential,
                node_type='whatsapp',
            )
            
            status_code = response["status_code"]
            if status_code == 200:
                return response["json"]
            else:
                error_data = response["json"] if response["json"] else {}
                error_msg = error_data.get('error', {}).get('message', 'Media upload failed')
                logger.error(f"WhatsApp API media upload error: {error_msg}")
                raise Exception(f"WhatsApp API media upload error: {error_msg}")

        except Exception as e:
            raise Exception(f"Media upload failed: {str(e)}")

    async def _get_media_url(self, media_id: str, credential: str) -> Dict[str, Any]:
        """
        Get media download URL from WhatsApp API.

        Args:
            media_id: Media ID
            access_token: WhatsApp API access token

        Returns:
            API response with media URL
        """

        return await self._make_api_request(
            'GET',
            media_id,
            credential,
            None,
        )
    
    async def run(self, data: NodeData) -> NodeResult:
        """
        Execute the WhatsApp node.
        
        Args:
            data: Node execution data containing parameters
            
        Returns:
            NodeResult with the execution result or error
        """
        self.data = data

        # workflow.logger.error(f"Running WhatsApp node with data: {data}")
        
        if not self.data or not self.data.parameters:
            return NodeResult(error="Invalid node data")
        
        try:
            # # Get credentials
            # credentials = self._get_credentials(data)
            # access_token = credentials['access_token']
            
            # Get common parameters
            resource = data.parameters.get('resource', 'message')
            phone_number_id = data.parameters.get('phoneNumberId')
            credential = data.credentials["whatsapp_api"].id if data.credentials and "whatsapp_api" in data.credentials else None

            if not credential:
                return NodeResult(error="WhatsApp API credential is required")

            if not phone_number_id or not isinstance(phone_number_id, str):
                return NodeResult(error="Phone Number ID is required")
            
            # Handle different resources and operations
            if resource == 'message':
                operation = data.parameters.get('operation', 'send')
                
                if operation == 'send':
                    return await self._handle_send_message(data, phone_number_id, credential)
                elif operation == 'sendTemplate':
                    return await self._handle_send_template(data, phone_number_id, credential)
                else:
                    return NodeResult(error=f"Unsupported message operation: {operation}")
                    
            elif resource == 'media':
                media_operation = data.parameters.get('mediaOperation', 'send')

                if media_operation == 'send':
                    return await self._handle_send_media(data,  phone_number_id, credential)
                elif media_operation == 'upload':
                    return await self._handle_upload_media(data,  phone_number_id, credential)
                elif media_operation == 'getUrl':
                    return await self._handle_get_media_url(data, credential)
                else:
                    return NodeResult(error=f"Unsupported media operation: {media_operation}")
            else:
                return NodeResult(error=f"Unsupported resource: {resource}")
                
        except Exception as e:
            return NodeResult(error=f"WhatsApp node execution failed: {str(e)}")
    
    async def _handle_send_message(self, data: NodeData, 
                                  phone_number_id: str, credential: str) -> NodeResult:
        """Handle sending a message."""
        recipient = data.parameters.get('recipientPhoneNumber')
        message_type = data.parameters.get('messageType', 'text')
        
        if not recipient or not isinstance(recipient, str):
            return NodeResult(error="Recipient phone number is required")
        
        try:
            if message_type == 'text':
                message = data.parameters.get('message')
                if not message or not isinstance(message, str):
                    return NodeResult(error="Message content is required")

                result = await self._send_text_message(
                    phone_number_id, recipient, message, credential
                )
            elif message_type == 'location':
                # Handle location messages
                latitude = data.parameters.get('latitude')
                longitude = data.parameters.get('longitude')

                if isinstance(latitude, str):
                    try:
                        latitude = float(latitude)
                    except ValueError:
                        return NodeResult(error="Invalid latitude value")
                
                if isinstance(longitude, str):
                    try:
                        longitude = float(longitude)
                    except ValueError:
                        return NodeResult(error="Invalid longitude value")

                if latitude is None or longitude is None or not isinstance(latitude, (float, int)) or not isinstance(longitude, (float, int)):
                    return NodeResult(error="Latitude and longitude are required for location messages")

                name = data.parameters.get('locationName')
                address = data.parameters.get('locationAddress')

                # Ensure name is a string or None
                if name is not None and not isinstance(name, str):
                    name = None

                # Ensure address is a string or None
                if address is not None and not isinstance(address, str):
                    address = None

                result = await self._send_location_message(
                    phone_number_id, recipient, latitude, longitude,
                    credential, name, address
                )
            else:
                if not isinstance(message_type, str):
                    return NodeResult(error="Message type must be a string")
                # Handle media messages
                media_url = data.parameters.get('mediaUrl')
                if not media_url or not isinstance(media_url, str):
                    return NodeResult(error="Media URL is required")

                caption = data.parameters.get('caption')
                filename = data.parameters.get('filename')

                if caption is not None and not isinstance(caption, str):
                    caption = None

                if filename is not None and not isinstance(filename, str):
                    filename = None

                result = await self._send_media_message(
                    phone_number_id, recipient, credential, message_type, media_url,
                    caption, filename
                )
            
            return NodeResult(result=result, next_connection_index=0)
            
        except Exception as e:
            return NodeResult(error=f"Failed to send message: {str(e)}")
    
    async def _handle_send_template(self, data: NodeData,
                                   phone_number_id: str, credential: str) -> NodeResult:
        """Handle sending a template message."""
        recipient = data.parameters.get('recipientPhoneNumber')
        template = data.parameters.get('template')
        template_params = data.parameters.get('templateParameters')

        if not recipient or not isinstance(recipient, str):
            return NodeResult(error="Recipient phone number is required")
        if not template or not isinstance(template, str):
            return NodeResult(error="Template is required")
        
        # Ensure template_params is a dictionary or None
        if template_params is not None and not isinstance(template_params, dict):
            template_params = None

        try:
            result = await self._send_template_message(
                phone_number_id, recipient, credential, template, template_params
            )
            return NodeResult(result=result, next_connection_index=0)

        except Exception as e:
            return NodeResult(error=f"Failed to send template: {str(e)}")
    
    async def _handle_send_media(self, data: NodeData, 
                                phone_number_id: str, credential: str) -> NodeResult:
        """Handle sending media."""
        recipient = data.parameters.get('recipientPhoneNumber')
        media_url = data.parameters.get('mediaUrl')
        
        if not recipient or not isinstance(recipient, str):
            return NodeResult(error="Recipient phone number is required")
        if not media_url or not isinstance(media_url, str):
            return NodeResult(error="Media URL is required")
        
        try:
            # For media resource, we'll default to image type
            # In a more complete implementation, this could be auto-detected
            # or specified as a parameter
            result = self._send_media_message(
                phone_number_id, recipient, credential, 'image', media_url
            )
            return NodeResult(result=result, next_connection_index=0)
            
        except Exception as e:
            return NodeResult(error=f"Failed to send media: {str(e)}")

    async def _handle_upload_media(self, data: NodeData,
                                  phone_number_id: str, credential: str) -> NodeResult:
        """Handle uploading media."""
        media_file = data.parameters.get('mediaFile')
        media_type = data.parameters.get('mediaType', 'image')

        if not media_file or not isinstance(media_file, str):
            return NodeResult(error="Media file is required")
        
        if not media_type or not isinstance(media_type, str):
            return NodeResult(error="Media type is required")

        try:
            result = await self._upload_media(
                phone_number_id, media_file, media_type, credential
            )
            return NodeResult(result=result, next_connection_index=0)

        except Exception as e:
            return NodeResult(error=f"Failed to upload media: {str(e)}")

    async def _handle_get_media_url(self, data: NodeData, credential: str) -> NodeResult:
        """Handle getting media URL."""
        media_id = data.parameters.get('mediaId')

        if not media_id or not isinstance(media_id, str):
            return NodeResult(error="Media ID is required")

        try:
            result = await self._get_media_url(media_id, credential)
            return NodeResult(result=result, next_connection_index=0)

        except Exception as e:
            return NodeResult(error=f"Failed to get media URL: {str(e)}")

    def validate(self, request: NodeRequest) -> ValidationResult:
        """
        Validate a WhatsApp node request with comprehensive checks.

        Args:
            request: The node request to validate

        Returns:
            ValidationResult with any validation errors
        """
        # Perform base validation first
        base_result = self.base_validate(request)
        if not base_result.valid:
            return base_result

        # Add WhatsApp-specific validation
        errors = []

        # Validate phone number ID format
        phone_number_id = request.parameters.get('phoneNumberId')
        if phone_number_id and not str(phone_number_id).isdigit():
            errors.append({
                'parameter': 'phoneNumberId',
                'message': 'Phone Number ID must contain only digits'
            })

        # Validate recipient phone number format
        recipient = request.parameters.get('recipientPhoneNumber')
        if recipient:
            sanitized = self._sanitize_phone_number(str(recipient))
            if not sanitized.isdigit() or len(sanitized) < 10:
                errors.append({
                    'parameter': 'recipientPhoneNumber',
                    'message': 'Recipient phone number must be valid (digits only, minimum 10 digits)'
                })

        # Validate template format
        # template = request.parameters.get('template')
        # if template and '|' not in template:
        #     errors.append({
        #         'parameter': 'template',
        #         'message': 'Template must be in format "template_name|language_code"'
        #     })

        # Validate location coordinates
        latitude = request.parameters.get('latitude')
        longitude = request.parameters.get('longitude')
        if latitude is not None:
            try:
                lat_val = float(str(latitude))
                if not -90 <= lat_val <= 90:
                    errors.append({
                        'parameter': 'latitude',
                        'message': 'Latitude must be between -90 and 90'
                    })
            except (ValueError, TypeError):
                errors.append({
                    'parameter': 'latitude',
                    'message': 'Latitude must be a valid number'
                })

        if longitude is not None:
            try:
                lon_val = float(str(longitude))
                if not -180 <= lon_val <= 180:
                    errors.append({
                        'parameter': 'longitude',
                        'message': 'Longitude must be between -180 and 180'
                    })
            except (ValueError, TypeError):
                errors.append({
                    'parameter': 'longitude',
                    'message': 'Longitude must be a valid number'
                })

        # Validate media URL format
        media_url = request.parameters.get('mediaUrl')
        if media_url and isinstance(media_url, str) and not (media_url.startswith('http://') or media_url.startswith('https://')):
            errors.append({
                'parameter': 'mediaUrl',
                'message': 'Media URL must be a valid HTTP or HTTPS URL'
            })

        return ValidationResult(
            valid=len(errors) == 0,
            errors=errors if errors else None
        )
