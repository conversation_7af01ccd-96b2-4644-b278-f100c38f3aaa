"""
Base Gmail Operation Module

This module provides base functionality and utilities for Gmail operations
including authentication, connection management, error handling, and common utilities.
"""

import asyncio
import logging
import json
import base64
from typing import Dict, Any, Optional, List, Union
from contextlib import asynccontextmanager
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.base import MI<PERSON>B<PERSON>
from email import encoders
import aiohttp
from pydantic import BaseModel, Field

from app.node.node_base.node import NodeResult
from app.node.node_base.node_models import NodeData

# Set up logging
logger = logging.getLogger(__name__)


class GmailConnectionConfig(BaseModel):
    """Configuration model for Gmail API connections."""
    
    client_id: str = Field(..., description="OAuth2 client ID")
    client_secret: str = Field(..., description="OAuth2 client secret")
    refresh_token: str = Field(..., description="OAuth2 refresh token")
    access_token: Optional[str] = Field(None, description="Current access token")
    user_email: str = Field(..., description="User email address")
    scopes: str = Field(
        "https://www.googleapis.com/auth/gmail.modify",
        description="OAuth2 scopes"
    )


class GmailConnectionManager:
    """
    Gmail API connection manager with OAuth2 token management.
    
    Provides secure authentication, token refresh, and session management
    for Gmail API operations.
    """
    
    GMAIL_API_BASE_URL = "https://gmail.googleapis.com/gmail/v1"
    OAUTH2_TOKEN_URL = "https://oauth2.googleapis.com/token"
    
    _sessions: Dict[str, aiohttp.ClientSession] = {}
    _tokens: Dict[str, str] = {}
    
    # @classmethod
    # async def get_connection_config(cls, data: NodeData) -> GmailConnectionConfig:
    #     """
    #     Extract Gmail connection configuration from node data.
        
    #     Args:
    #         data: Node execution data containing credentials
            
    #     Returns:
    #         GmailConnectionConfig: Connection configuration
            
    #     Raises:
    #         ValueError: If credentials are missing or invalid
    #     """
    #     if not data.credentials or 'gmail' not in data.credentials:
    #         raise ValueError("Gmail API credentials are required")
        
    #     cred = data.credentials['gmail']
  
    #     # Extract credential data
    #     try:
    #         # Use the proper credential manager to get credential data
    #         from app.credential.utils.credential_manager import CredentialManager
    #         credential_manager = CredentialManager()
            
    #         # Get credential ID - handle both string and object cases
    #         credential_id = cred.id if hasattr(cred, 'id') else str(cred)
    #         cred_data = await credential_manager.get_credential(credential_id, "gmail")
            
    #         if not cred_data:
    #             raise ValueError("Failed to retrieve credential data")
                
    #     except Exception as e:
    #         logger.error(f"Failed to extract Gmail credentials: {str(e)}")
    #         raise ValueError(f"Invalid Gmail credential format: {str(e)}")
        
    #     return GmailConnectionConfig(
    #         client_id=cred_data.get('client_id', ''),
    #         client_secret=cred_data.get('client_secret', ''),
    #         refresh_token=cred_data.get('refresh_token', ''),
    #         access_token=cred_data.get('access_token'),
    #         user_email=cred_data.get('user_email', ''),
    #         scopes=cred_data.get('scopes', 'https://www.googleapis.com/auth/gmail.modify')
    #     )
    
    # @classmethod
    # async def refresh_access_token(cls, config: GmailConnectionConfig) -> str:
    #     """
    #     Refresh OAuth2 access token using refresh token.
        
    #     Args:
    #         config: Gmail connection configuration
            
    #     Returns:
    #         str: New access token
            
    #     Raises:
    #         ValueError: If token refresh fails
    #     """
    #     token_data = {
    #         'client_id': config.client_id,
    #         'client_secret': config.client_secret,
    #         'refresh_token': config.refresh_token,
    #         'grant_type': 'refresh_token'
    #     }
        
    #     async with aiohttp.ClientSession() as session:
    #         async with session.post(cls.OAUTH2_TOKEN_URL, data=token_data) as response:
    #             if response.status != 200:
    #                 error_text = await response.text()
    #                 raise ValueError(f"Failed to refresh access token: {error_text}")
                
    #             token_response = await response.json()
    #             if 'access_token' not in token_response:
    #                 raise ValueError("No access token in refresh response")
                
    #             return token_response['access_token']
    
    # @classmethod
    # async def get_valid_access_token(cls, config: GmailConnectionConfig) -> str:
    #     """
    #     Get a valid access token, refreshing if necessary.
        
    #     Args:
    #         config: Gmail connection configuration
            
    #     Returns:
    #         str: Valid access token
    #     """
    #     # Create a unique key for this configuration
    #     config_key = f"{config.user_email}:{config.client_id}"
        
    #     # Check if we have a cached token
    #     if config_key in cls._tokens:
    #         return cls._tokens[config_key]
        
    #     # Use provided access token or refresh
    #     if config.access_token:
    #         access_token = config.access_token
    #     else:
    #         access_token = await cls.refresh_access_token(config)
        
    #     # Cache the token
    #     cls._tokens[config_key] = access_token
    #     return access_token
    
    # @classmethod
    # @asynccontextmanager
    # async def get_session(cls, data: NodeData):
    #     """
    #     Get an authenticated HTTP session for Gmail API.
        
    #     Args:
    #         data: Node execution data
            
    #     Yields:
    #         tuple: (session, access_token, user_email)
    #     """
    #     config = await cls.get_connection_config(data)
    #     access_token = await cls.get_valid_access_token(config)
        
    #     # Create session key
    #     session_key = f"{config.user_email}:{config.client_id}"
        
    #     # Get or create session
    #     if session_key not in cls._sessions or cls._sessions[session_key].closed:
    #         headers = {
    #             'Authorization': f'Bearer {access_token}',
    #             'Content-Type': 'application/json'
    #         }
    
    #         cls._sessions[session_key] = aiohttp.ClientSession(
    #             headers=headers,
    #             timeout=aiohttp.ClientTimeout(total=30)
    #         )
        
    #     try:
    #         yield cls._sessions[session_key], access_token, config.user_email
    #     except aiohttp.ClientResponseError as e:
    #         if e.status == 401:
    #             # Token expired, refresh and retry
    #             logger.info("Access token expired, refreshing...")
    #             access_token = await cls.refresh_access_token(config)
    #             cls._tokens[session_key] = access_token
                
    #             # Update session headers
    #             cls._sessions[session_key].headers.update({
    #                 'Authorization': f'Bearer {access_token}'
    #             })
                
    #             yield cls._sessions[session_key], access_token, config.user_email
    #         else:
    #             raise
    #     except Exception as e:
    #         logger.error(f"Gmail API operation failed: {str(e)}")
    #         raise
    
    # @classmethod
    # async def close_all_sessions(cls):
    #     """Close all HTTP sessions."""
    #     for session_key, session in cls._sessions.items():
    #         if not session.closed:
    #             await session.close()
    #             logger.info(f"Closed Gmail session: {session_key}")
    #     cls._sessions.clear()
    #     cls._tokens.clear()


class BaseOperation:
    """
    Base class for Gmail operations.
    
    Provides common functionality for Gmail operations including
    error handling, parameter validation, and result formatting.
    """
    
    @staticmethod
    def format_error(error: Exception, operation: str) -> str:
        """
        Format Gmail API errors into user-friendly messages.
        
        Args:
            error: The exception that occurred
            operation: The operation being performed
            
        Returns:
            str: Formatted error message
        """
        if isinstance(error, aiohttp.ClientResponseError):
            return f"Gmail API {operation} error (HTTP {error.status}): {error.message}"
        elif isinstance(error, aiohttp.ClientConnectionError):
            return f"Gmail connection error during {operation}: {str(error)}"
        elif isinstance(error, asyncio.TimeoutError):
            return f"Gmail {operation} operation timed out"
        elif isinstance(error, ValueError):
            return f"Gmail {operation} validation error: {str(error)}"
        else:
            return f"Gmail {operation} operation failed: {str(error)}"
    
    @staticmethod
    def format_results(data: Any, operation: str, **metadata) -> Dict[str, Any]:
        """
        Format Gmail API results into a standardized structure.
        
        Args:
            data: Gmail API response data
            operation: The operation that was performed
            **metadata: Additional metadata to include
            
        Returns:
            Dict containing formatted results
        """
        result = {
            "operation": operation,
            "data": data,
            **metadata
        }
        
        # Add count information if data is a list
        if isinstance(data, list):
            result["count"] = len(data)
        
        return result
    
    @staticmethod
    def validate_email_address(email: str) -> bool:
        """
        Validate email address format.
        
        Args:
            email: Email address to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        if not email or not isinstance(email, str):
            return False
        
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    @staticmethod
    def parse_email_list(emails: Union[str, List[str]]) -> List[str]:
        """
        Parse email addresses from string or list format.
        
        Args:
            emails: Email addresses as string (comma-separated) or list
            
        Returns:
            List[str]: List of email addresses
            
        Raises:
            ValueError: If email format is invalid
        """
        if isinstance(emails, str):
            email_list = [email.strip() for email in emails.split(',') if email.strip()]
        elif isinstance(emails, list):
            email_list = [str(email).strip() for email in emails if str(email).strip()]
        else:
            raise ValueError("Emails must be a string or list")
        
        # Validate all email addresses
        for email in email_list:
            if not BaseOperation.validate_email_address(email):
                raise ValueError(f"Invalid email address: {email}")
        
        return email_list
    
    @staticmethod
    def encode_message(message: MIMEMultipart) -> str:
        """
        Encode email message for Gmail API.
        
        Args:
            message: MIME message object
            
        Returns:
            str: Base64 encoded message
        """
        raw_message = message.as_bytes()
        return base64.urlsafe_b64encode(raw_message).decode('utf-8')
