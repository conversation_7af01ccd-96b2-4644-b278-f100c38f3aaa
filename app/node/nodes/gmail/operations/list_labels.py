"""
Gmail List Labels Operation

This module implements the list labels operation for Gmail nodes,
providing the ability to retrieve all available labels.
"""

import logging
from typing import Dict, Any, List
import aiohttp

from app.node.node_base.node import NodeResult
from app.node.node_base.node_models import NodeData
from .base_operation import BaseOperation, GmailConnectionManager

# Set up logging
logger = logging.getLogger(__name__)


class ListLabelsOperation(BaseOperation):
    """
    Gmail LIST LABELS operation implementation.
    
    Provides the ability to retrieve all Gmail labels including:
    - System labels (INBOX, SENT, DRAFT, etc.)
    - User-created labels
    - Label metadata (name, type, visibility, etc.)
    - Proper error handling and result formatting
    """
    
    @staticmethod
    async def execute(data: NodeData) -> NodeResult:
        """
        Execute a LIST LABELS operation on Gmail.
        
        Args:
            data: Node execution data containing filter parameters
            
        Returns:
            NodeResult: List of labels or error information
        """
        try:
            # Extract parameters
            include_system = data.parameters.get('include_system', True)
            include_user = data.parameters.get('include_user', True)
            
            logger.info(f"Listing Gmail labels (system: {include_system}, user: {include_user})")
            
            try:
                # Get labels using HttpClient
                labels = await ListLabelsOperation._fetch_labels(data)
                
                # Filter labels based on parameters
                filtered_labels = []
                for label in labels:
                    label_type = label.get('type', '')
                    
                    if (label_type == 'system' and include_system) or \
                       (label_type == 'user' and include_user):
                        filtered_labels.append(label)
                
                logger.info(f"Found {len(filtered_labels)} Gmail labels")
                
                # Return the correct format - use 'result' parameter instead of 'data'
                return NodeResult(result=filtered_labels)
                
            except Exception as e:
                return NodeResult(error=f"Unexpected error listing labels: {str(e)}")
    
        except Exception as e:
            error_msg = ListLabelsOperation.format_error(e, "list labels")
            logger.error(f"List labels operation failed: {error_msg}")
            return NodeResult(error=error_msg)
    
    @staticmethod
    async def _fetch_labels(data: NodeData) -> List[Dict[str, Any]]:
        """
        Fetch all labels from Gmail API using HttpClient.
        
        Args:
            data: NodeData containing credentials
            
        Returns:
            List[Dict[str, Any]]: List of labels
        """
        from app.utils.http_client import HttpClient, HttpMethod
        
        url = f"{GmailConnectionManager.GMAIL_API_BASE_URL}/users/me/labels"
        
        # Use HttpClient to get labels
        response = await HttpClient.request(
            method=HttpMethod.GET,
            url=url,
            credential=data.credentials['gmail'].id,
            node_type=data.type
        )
        
        # Handle response
        if response["status_code"] != 200:
            error_text = response.get("text", "Unknown error")
            raise ValueError(f"Failed to fetch labels: {error_text}")
        
        result = response["json"]
        labels = result.get("labels", [])
        
        # Process and format labels
        formatted_labels = []
        for label in labels:
            formatted_label = {
                'id': label.get('id', ''),
                'name': label.get('name', ''),
                'type': label.get('type', 'user'),
                'messages_total': label.get('messagesTotal', 0),
                'messages_unread': label.get('messagesUnread', 0),
                'threads_total': label.get('threadsTotal', 0),
                'threads_unread': label.get('threadsUnread', 0),
                'label_list_visibility': label.get('labelListVisibility', ''),
                'message_list_visibility': label.get('messageListVisibility', ''),
                'color': label.get('color', {})
            }
            formatted_labels.append(formatted_label)
        
        return formatted_labels
