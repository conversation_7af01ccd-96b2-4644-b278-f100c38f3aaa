"""
MongoDB Base Operation

This module provides the base class for all MongoDB operations with common
functionality including connection management, error handling, and result formatting.
"""

import json
import logging
from typing import Dict, Any
from contextlib import asynccontextmanager
from urllib.parse import quote_plus

from pymongo.errors import (
    ConnectionFailure, 
    ServerSelectionTimeoutError, 
    OperationFailure, 
    DuplicateKeyError,
    BulkWriteError,
    InvalidOperation,
    ConfigurationError
)

from app.node.node_base.node_models import NodeData
from temporalio import workflow

# Set up logging
logger = logging.getLogger(__name__)

with workflow.unsafe.imports_passed_through():
    from app.utils.db_util import db_session


class MongoDBConnectionManager:
    """
    MongoDB connection manager for handling database connections.
    
    Provides secure connection management with proper credential handling,
    connection pooling, and error handling for MongoDB operations.
    """
    
    @staticmethod
    def _build_connection_string(credentials: Dict[str, Any]) -> str:
        """
        Build MongoDB connection string from credentials.
        
        Args:
            credentials: Credential data containing connection parameters
            
        Returns:
            str: MongoDB connection string
            
        Raises:
            ValueError: If required credentials are missing or invalid
        """
        config_type = credentials.get('configuration_type', 'connection_string')
        
        if config_type == 'connection_string':
            connection_string = credentials.get('connection_string')
            if not connection_string:
                raise ValueError("Connection string is required when using connection_string configuration")
            return connection_string
        
        elif config_type == 'parameters':
            # Build connection string from individual parameters
            host = credentials.get('host', 'localhost')
            port = credentials.get('port', 27017)
            database = credentials.get('database')
            username = credentials.get('username')
            password = credentials.get('password')
            auth_database = credentials.get('auth_database', 'admin')
            use_ssl = credentials.get('use_ssl', False)
            
            if not database:
                raise ValueError("Database name is required")
            
            # Build connection string
            if username and password:
                # URL encode username and password to handle special characters
                encoded_username = quote_plus(username)
                encoded_password = quote_plus(password)
                connection_string = f"mongodb://{encoded_username}:{encoded_password}@{host}:{port}/{database}"
                
                # Add authentication database if different from target database
                if auth_database and auth_database != database:
                    connection_string += f"?authSource={auth_database}"
                    
                # Add SSL option if enabled
                if use_ssl:
                    separator = "&" if "?" in connection_string else "?"
                    connection_string += f"{separator}ssl=true"
            else:
                connection_string = f"mongodb://{host}:{port}/{database}"
                if use_ssl:
                    connection_string += "?ssl=true"
            
            return connection_string
        
        else:
            raise ValueError(f"Unsupported configuration type: {config_type}")
    
    @staticmethod
    def _get_database_name(credentials: Dict[str, Any]) -> str:
        """
        Extract database name from credentials.
        
        Args:
            credentials: Credential data
            
        Returns:
            str: Database name
            
        Raises:
            ValueError: If database name cannot be determined
        """
        config_type = credentials.get('configuration_type', 'connection_string')
        if config_type == 'connection_string':
            # For connection string, we'll need to parse it or require it in credentials
            database = credentials.get('database')
            if not database:
                raise ValueError("Database name must be provided when using connection string configuration")
            return database
        
        elif config_type == 'parameters':
            database = credentials.get('database')
            if not database:
                raise ValueError("Database name is required in parameters configuration")
            return database
        
        else:
            raise ValueError(f"Unsupported configuration type: {config_type}")
    
    @staticmethod
    @asynccontextmanager
    async def get_connection(data: NodeData):
        """
        Get a MongoDB database connection using credentials from node data.
        
        Args:
            data: Node execution data containing credentials
            
        Yields:
            AsyncIOMotorDatabase: MongoDB database connection
            
        Raises:
            ValueError: If credentials are missing or invalid
            ConnectionFailure: If connection to MongoDB fails
        """
        if not data.credentials or 'mongodb' not in data.credentials:
            raise ValueError("MongoDB credentials are required")
        
        from app.credential.utils.credential_manager import CredentialManager

        credential_manager = CredentialManager()
        auth_generator = await credential_manager.get_custom_authentication(data.credentials['mongodb'].id, "mongodb")

        try:
            async for db in auth_generator:
                yield db
                break  # We only need the first database connection
        except Exception as e:
            raise ConnectionFailure(f"Failed to connect to MongoDB: {str(e)}")    

class BaseOperation:
    """
    Base class for MongoDB operations.
    
    Provides common functionality for all MongoDB operations including
    error handling, result formatting, and validation utilities.
    """
    
    @staticmethod
    def parse_json_parameter(value: Any, parameter_name: str) -> Any:
        """
        Parse a JSON parameter value, handling both strings and objects.
        
        Args:
            value: Parameter value to parse
            parameter_name: Name of the parameter (for error messages)
            
        Returns:
            Parsed JSON value
            
        Raises:
            ValueError: If JSON parsing fails
        """
        if value is None:
            return {}
        
        if isinstance(value, str):
            if not value.strip():
                return {}
            try:
                return json.loads(value)
            except json.JSONDecodeError as e:
                raise ValueError(f"Invalid JSON in {parameter_name}: {str(e)}")
        
        # If it's already a dict/list, return as-is
        if isinstance(value, (dict, list)):
            return value
        
        raise ValueError(f"Invalid type for {parameter_name}: expected JSON string or object")
    
    @staticmethod
    def format_error(error: Exception, operation: str) -> str:
        """
        Format MongoDB errors into user-friendly messages.
        
        Args:
            error: The exception that occurred
            operation: The operation being performed
            
        Returns:
            str: Formatted error message
        """
        if isinstance(error, ConnectionFailure):
            return f"MongoDB connection failed during {operation}: {str(error)}"
        elif isinstance(error, ServerSelectionTimeoutError):
            return f"MongoDB server selection timeout during {operation}: {str(error)}"
        elif isinstance(error, OperationFailure):
            return f"MongoDB {operation} operation failed: {str(error)}"
        elif isinstance(error, DuplicateKeyError):
            return f"MongoDB {operation} failed due to duplicate key: {str(error)}"
        elif isinstance(error, BulkWriteError):
            return f"MongoDB bulk {operation} failed: {str(error)}"
        elif isinstance(error, InvalidOperation):
            return f"Invalid MongoDB {operation} operation: {str(error)}"
        elif isinstance(error, ConfigurationError):
            return f"MongoDB configuration error during {operation}: {str(error)}"
        elif isinstance(error, ValueError):
            return f"MongoDB {operation} validation error: {str(error)}"
        else:
            return f"MongoDB {operation} operation failed: {str(error)}"
    
    @staticmethod
    def format_results(result: Any, operation: str, **kwargs) -> Dict[str, Any]:
        """
        Format MongoDB results into a standardized structure.
        
        Args:
            result: MongoDB operation result
            operation: The operation that was performed
            **kwargs: Additional metadata to include
            
        Returns:
            Dict containing formatted results
        """
        formatted_result = {
            "operation": operation,
            "success": True
        }
        
        # Add operation-specific formatting
        if operation in ["find", "aggregate"]:
            # For find/aggregate operations, result should be a list of documents
            if isinstance(result, list):
                formatted_result["documents"] = result
                formatted_result["count"] = len(result)
            else:
                formatted_result["documents"] = [result] if result else []
                formatted_result["count"] = 1 if result else 0
                
        elif operation in ["insert"]:
            # For insert operations
            formatted_result["inserted_ids"] = result.get("inserted_ids", [])
            formatted_result["inserted_count"] = result.get("inserted_count", 0)
            
        elif operation in ["update", "findOneAndUpdate"]:
            # For update operations
            formatted_result["matched_count"] = result.get("matched_count", 0)
            formatted_result["modified_count"] = result.get("modified_count", 0)
            formatted_result["upserted_id"] = result.get("upserted_id")
            
        elif operation in ["delete"]:
            # For delete operations
            formatted_result["deleted_count"] = result.get("deleted_count", 0)
            
        elif operation in ["count"]:
            # For count operations
            formatted_result["count"] = result
            
        else:
            # For other operations, include the raw result
            formatted_result["result"] = result
        
        # Add any additional metadata
        formatted_result.update(kwargs)
        
        return formatted_result
    
    @staticmethod
    def validate_collection_name(collection_name: Any) -> bool:
        """
        Validate MongoDB collection name.
        
        Args:
            collection_name: Collection name to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        if not collection_name or not isinstance(collection_name, str):
            return False
        
        # MongoDB collection name restrictions
        if collection_name.startswith('system.'):
            return False
        
        # Check for invalid characters
        invalid_chars = ['$', '\0']
        for char in invalid_chars:
            if char in collection_name:
                return False
        
        return True
