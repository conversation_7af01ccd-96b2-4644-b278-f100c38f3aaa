"""
MongoDB Count Operation

This module implements the MongoDB count operation for counting documents
in collections with optional query filtering.
"""

import logging
from typing import Dict, Any, List, Optional

from app.node.node_base.node_models import NodeData
from app.node.node_base.node import NodeResult
from .base_operation import BaseOperation, MongoDBConnectionManager
from motor.motor_asyncio import AsyncIOMotorDatabase

# Set up logging
logger = logging.getLogger(__name__)


class CountOperation(BaseOperation):
    """
    MongoDB COUNT operation implementation.
    
    Provides document counting capabilities including:
    - Count all documents in a collection
    - Count documents matching a query filter
    - Estimated document count for performance
    - Proper error handling and result formatting
    """
    
    @staticmethod
    async def execute(data: NodeData, database: AsyncIOMotorDatabase) -> NodeResult:
        """
        Execute a COUNT operation on MongoDB.
        
        Args:
            data: Node execution data containing count parameters
            
        Returns:
            NodeResult: Count results or error information
        """
        try:
            # Extract parameters
            collection_name = data.parameters.get('collection')
            query = data.parameters.get('query', '{}')
            options = data.parameters.get('options', '{}')
            timeout = data.parameters.get('timeout', 30)
            
            # Validate required parameters
            if not collection_name:
                return NodeResult(error="Collection name is required for COUNT operation")
            
            if not CountOperation.validate_collection_name(collection_name):
                return NodeResult(error=f"Invalid collection name: {collection_name}")
            
            # Parse JSON parameters
            try:
                parsed_query = CountOperation.parse_json_parameter(query, 'query')
                parsed_options = CountOperation.parse_json_parameter(options, 'options')
            except ValueError as e:
                return NodeResult(error=str(e))
            
            # Extract options
            use_estimated = parsed_options.get('estimated', False)
            limit = parsed_options.get('limit')
            skip = parsed_options.get('skip')
            hint = parsed_options.get('hint')
            max_time_ms = parsed_options.get('maxTimeMS')
            collation = parsed_options.get('collation')
            
            logger.info(f"Executing MongoDB COUNT on collection: {collection_name}")
            logger.debug(f"Query: {parsed_query}")
            logger.debug(f"Use estimated: {use_estimated}")
            
            collection = database[str(collection_name)]
                
            if use_estimated and (not parsed_query or parsed_query == {}):
                # Use estimated_document_count for better performance when no query filter
                count = await collection.estimated_document_count()
                count_method = "estimated_document_count"
            else:
                # Use count_documents for accurate count with query filter
                count_options = {}
                
                if limit is not None:
                    count_options['limit'] = limit
                
                if skip is not None:
                    count_options['skip'] = skip
                
                if hint:
                    count_options['hint'] = hint
                
                if max_time_ms:
                    count_options['maxTimeMS'] = max_time_ms
                
                if collation:
                    count_options['collation'] = collation
                
                count = await collection.count_documents(parsed_query, **count_options)
                count_method = "count_documents"
            
            # Format results
            result_data = CountOperation.format_results(
                count,
                "count",
                collection=collection_name,
                query=parsed_query,
                method=count_method,
                estimated=use_estimated,
                options=parsed_options
            )
                
            logger.info(f"MongoDB COUNT completed successfully. Count: {count} (method: {count_method})")
            return NodeResult(result=result_data)
                
        except Exception as e:
            error_message = CountOperation.format_error(e, "COUNT")
            logger.error(f"MongoDB COUNT operation failed: {error_message}")
            return NodeResult(error=error_message)
