"""
MongoDB Update Operation

This module implements the MongoDB update operation for modifying documents
in collections with support for single and multiple document updates.
"""

import logging
from typing import Dict, Any, List, Optional

from app.node.node_base.node_models import NodeData
from app.node.node_base.node import NodeResult
from .base_operation import BaseOperation, MongoDBConnectionManager
from motor.motor_asyncio import AsyncIOMotorDatabase

# Set up logging
logger = logging.getLogger(__name__)


class UpdateOperation(BaseOperation):
    """
    MongoDB UPDATE operation implementation.
    
    Provides comprehensive document update capabilities including:
    - Single document updates (updateOne)
    - Multiple document updates (updateMany)
    - Upsert operations (insert if not found)
    - Array filters for complex updates
    - Proper error handling and result formatting
    """
    
    @staticmethod
    async def execute(data: NodeData, database: AsyncIOMotorDatabase) -> NodeResult:
        """
        Execute an UPDATE operation on MongoDB.
        
        Args:
            data: Node execution data containing update parameters
            
        Returns:
            NodeResult: Update results or error information
        """
        try:
            # Extract parameters
            collection_name = data.parameters.get('collection')
            query = data.parameters.get('query', '{}')
            update = data.parameters.get('update', '{}')
            options = data.parameters.get('options', '{}')
            upsert = data.parameters.get('upsert', False)
            multi = data.parameters.get('multi', False)
            timeout = data.parameters.get('timeout', 30)
            
            print(f"UpdateOperation:data111: {data}")

            # Validate required parameters
            if not collection_name:
                return NodeResult(error="Collection name is required for UPDATE operation")
            
            if not UpdateOperation.validate_collection_name(collection_name):
                return NodeResult(error=f"Invalid collection name: {collection_name}")
            
            # Parse JSON parameters
            try:
                parsed_query = UpdateOperation.parse_json_parameter(query, 'query')
                parsed_update = UpdateOperation.parse_json_parameter(update, 'update')
                parsed_options = UpdateOperation.parse_json_parameter(options, 'options')
            except ValueError as e:
                return NodeResult(error=str(e))
            
            # Validate update parameter
            if not parsed_update:
                return NodeResult(error="Update data is required for UPDATE operation")
            
            # Validate update operators
            if not isinstance(parsed_update, dict):
                return NodeResult(error="Update data must be a valid JSON object")
            
            # Check if update contains valid update operators
            valid_operators = {
                '$set', '$unset', '$inc', '$mul', '$rename', '$min', '$max',
                '$currentDate', '$addToSet', '$pop', '$pull', '$push',
                '$pullAll', '$each', '$slice', '$sort', '$position'
            }
            
            # If no update operators are present, wrap in $set
            if not any(key.startswith('$') for key in parsed_update.keys()):
                parsed_update = {'$set': parsed_update}
            
            # Extract additional options
            array_filters = parsed_options.get('array_filters', [])
            bypass_document_validation = parsed_options.get('bypass_document_validation', False)
            
            logger.info(f"Executing MongoDB UPDATE on collection: {collection_name}")
            logger.debug(f"Query: {parsed_query}")
            logger.debug(f"Update: {parsed_update}")
            logger.debug(f"Multi: {multi}, Upsert: {upsert}")
            
            collection = database[str(collection_name)]
            
            # Prepare update options
            update_options = {
                'upsert': upsert,
                'bypass_document_validation': bypass_document_validation
            }
            
            if array_filters:
                update_options['array_filters'] = array_filters
            
            print(f"UpdateOperation.execute:update_options111: {update_options}")
            print(f"UpdateOperation.execute:parsed_query111: {parsed_query}")
            print(f"UpdateOperation.execute:parsed_update111: {parsed_update}")
            print(f"UpdateOperation.execute:multi111: {multi}")

            if multi:
                # Update multiple documents
                result = await collection.update_many(
                    parsed_query,
                    parsed_update,
                    **update_options
                )
            else:
                # Update single document
                result = await collection.update_one(
                    parsed_query,
                    parsed_update,
                    **update_options
                )
            
            print(f"UpdateOperation.execute:result111: {result}")
            
            # Format update result
            update_result = {
                "matched_count": result.matched_count,
                "modified_count": result.modified_count,
                "upserted_id": str(result.upserted_id) if result.upserted_id else None
            }
            
            # Format results
            result_data = UpdateOperation.format_results(
                update_result,
                "update",
                collection=collection_name,
                query=parsed_query,
                update=parsed_update,
                multi=multi,
                upsert=upsert
            )
            
            logger.info(f"MongoDB UPDATE completed successfully. "
                        f"Matched: {result.matched_count}, Modified: {result.modified_count}")
            
            if result.upserted_id:
                logger.info(f"Upserted document with ID: {result.upserted_id}")
            
            return NodeResult(result=result_data)
                
        except Exception as e:
            error_message = UpdateOperation.format_error(e, "UPDATE")
            logger.error(f"MongoDB UPDATE operation failed: {error_message}")
            return NodeResult(error=error_message)
