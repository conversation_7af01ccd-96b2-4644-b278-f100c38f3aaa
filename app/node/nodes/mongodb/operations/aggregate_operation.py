"""
MongoDB Aggregate Operation

This module implements the MongoDB aggregation operation for complex data
processing using aggregation pipelines.
"""

import logging
from typing import Dict, Any, List, Optional

from app.node.node_base.node_models import NodeData
from app.node.node_base.node import NodeResult
from .base_operation import BaseOperation, MongoDBConnectionManager
from motor.motor_asyncio import AsyncIOMotorDatabase

# Set up logging
logger = logging.getLogger(__name__)


class AggregateOperation(BaseOperation):
    """
    MongoDB AGGREGATE operation implementation.
    
    Provides comprehensive aggregation pipeline capabilities including:
    - Multi-stage aggregation pipelines
    - Support for all MongoDB aggregation operators
    - Cursor options for large result sets
    - Proper error handling and result formatting
    """
    
    @staticmethod
    async def execute(data: NodeData, database: AsyncIOMotorDatabase) -> NodeResult:
        """
        Execute an AGGREGATE operation on MongoDB.
        
        Args:
            data: Node execution data containing aggregation parameters
            
        Returns:
            NodeResult: Aggregation results or error information
        """
        try:
            # Extract parameters
            collection_name = data.parameters.get('collection')
            pipeline = data.parameters.get('pipeline', '[]')
            options = data.parameters.get('options', '{}')
            timeout = data.parameters.get('timeout', 30)
            
            # Validate required parameters
            if not collection_name:
                return NodeResult(error="Collection name is required for AGGREGATE operation")
            
            if not AggregateOperation.validate_collection_name(collection_name):
                return NodeResult(error=f"Invalid collection name: {collection_name}")
            
            # Parse JSON parameters
            try:
                parsed_pipeline = AggregateOperation.parse_json_parameter(pipeline, 'pipeline')
                parsed_options = AggregateOperation.parse_json_parameter(options, 'options')
            except ValueError as e:
                return NodeResult(error=str(e))
            
            # Validate pipeline parameter
            if not isinstance(parsed_pipeline, list):
                return NodeResult(error="Pipeline must be a JSON array of aggregation stages")
            
            if not parsed_pipeline:
                return NodeResult(error="Pipeline cannot be empty for AGGREGATE operation")
            
            # Validate each stage in the pipeline
            for i, stage in enumerate(parsed_pipeline):
                if not isinstance(stage, dict):
                    return NodeResult(error=f"Pipeline stage {i} must be a valid JSON object")
                
                # Check if stage has at least one operator
                if not stage:
                    return NodeResult(error=f"Pipeline stage {i} cannot be empty")
                
                # Validate that stage keys start with $ (aggregation operators)
                for key in stage.keys():
                    if not key.startswith('$'):
                        return NodeResult(error=f"Invalid aggregation operator '{key}' in stage {i}. "
                                                "Aggregation operators must start with '$'")
            
            # Extract aggregation options
            allow_disk_use = parsed_options.get('allowDiskUse', False)
            max_time_ms = parsed_options.get('maxTimeMS')
            batch_size = parsed_options.get('batchSize')
            collation = parsed_options.get('collation')
            hint = parsed_options.get('hint')
            comment = parsed_options.get('comment')
            
            logger.info(f"Executing MongoDB AGGREGATE on collection: {collection_name}")
            logger.debug(f"Pipeline stages: {len(parsed_pipeline)}")
            logger.debug(f"Pipeline: {parsed_pipeline}")
            
            collection = database[str(collection_name)]
            
            # Prepare aggregation options
            agg_options = {
                'allowDiskUse': allow_disk_use
            }
            
            if max_time_ms:
                agg_options['maxTimeMS'] = max_time_ms
            
            if batch_size:
                agg_options['batchSize'] = batch_size
            
            if collation:
                agg_options['collation'] = collation
            
            if hint:
                agg_options['hint'] = hint
            
            if comment:
                agg_options['comment'] = comment
            
            # Execute aggregation
            cursor = collection.aggregate(parsed_pipeline, **agg_options)
            
            # Convert cursor to list
            documents = await cursor.to_list(length=None)
            
            # Convert ObjectId to string for JSON serialization
            for doc in documents:
                if isinstance(doc, dict) and '_id' in doc:
                    doc['_id'] = str(doc['_id'])
            
            # Format results
            result_data = AggregateOperation.format_results(
                documents,
                "aggregate",
                collection=collection_name,
                pipeline=parsed_pipeline,
                pipeline_stages=len(parsed_pipeline),
                options=parsed_options
            )
            
            logger.info(f"MongoDB AGGREGATE completed successfully. "
                        f"Processed {len(parsed_pipeline)} stages, returned {len(documents)} documents")
            return NodeResult(result=result_data)
                
        except Exception as e:
            error_message = AggregateOperation.format_error(e, "AGGREGATE")
            logger.error(f"MongoDB AGGREGATE operation failed: {error_message}")
            return NodeResult(error=error_message)
