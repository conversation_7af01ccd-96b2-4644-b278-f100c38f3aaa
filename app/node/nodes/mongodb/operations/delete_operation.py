"""
MongoDB Delete Operation

This module implements the MongoDB delete operation for removing documents
from collections with support for single and multiple document deletion.
"""

import logging
from typing import Dict, Any, List, Optional

from app.node.node_base.node_models import NodeData
from app.node.node_base.node import NodeResult
from .base_operation import BaseOperation, MongoDBConnectionManager
from motor.motor_asyncio import AsyncIOMotorDatabase

# Set up logging
logger = logging.getLogger(__name__)


class DeleteOperation(BaseOperation):
    """
    MongoDB DELETE operation implementation.
    
    Provides comprehensive document deletion capabilities including:
    - Single document deletion (deleteOne)
    - Multiple document deletion (deleteMany)
    - Safety checks to prevent accidental full collection deletion
    - Proper error handling and result formatting
    """
    
    @staticmethod
    async def execute(data: NodeData, database: AsyncIOMotorDatabase) -> NodeResult:
        """
        Execute a DELETE operation on MongoDB.
        
        Args:
            data: Node execution data containing delete parameters
            
        Returns:
            NodeResult: Delete results or error information
        """
        try:
            # Extract parameters
            collection_name = data.parameters.get('collection')
            query = data.parameters.get('query', '{}')
            options = data.parameters.get('options', '{}')
            multi = data.parameters.get('multi', False)
            timeout = data.parameters.get('timeout', 30)
            
            # Validate required parameters
            if not collection_name:
                return NodeResult(error="Collection name is required for DELETE operation")
            
            if not DeleteOperation.validate_collection_name(collection_name):
                return NodeResult(error=f"Invalid collection name: {collection_name}")
            
            # Parse JSON parameters
            try:
                parsed_query = DeleteOperation.parse_json_parameter(query, 'query')
                parsed_options = DeleteOperation.parse_json_parameter(options, 'options')
            except ValueError as e:
                return NodeResult(error=str(e))
            
            # Safety check: prevent accidental deletion of entire collection
            if not parsed_query or parsed_query == {}:
                # Allow empty query only if explicitly confirmed in options
                allow_empty_query = parsed_options.get('allow_empty_query', False)
                if not allow_empty_query:
                    return NodeResult(
                        error="Empty query would delete all documents. "
                              "Set 'allow_empty_query': true in options to confirm this action."
                    )
            
            # Extract additional options
            collation = parsed_options.get('collation')
            hint = parsed_options.get('hint')
            
            logger.info(f"Executing MongoDB DELETE on collection: {collection_name}")
            logger.debug(f"Query: {parsed_query}")
            logger.debug(f"Multi: {multi}")
            
            collection = database[str(collection_name)]
            
            # Prepare delete options
            delete_options = {}
            
            if collation:
                delete_options['collation'] = collation
            
            if hint:
                delete_options['hint'] = hint
            
            if multi:
                # Delete multiple documents
                result = await collection.delete_many(
                    parsed_query,
                    **delete_options
                )
            else:
                # Delete single document
                result = await collection.delete_one(
                    parsed_query,
                    **delete_options
                )
            
            # Format delete result
            delete_result = {
                "deleted_count": result.deleted_count
            }
            
            # Format results
            result_data = DeleteOperation.format_results(
                delete_result,
                "delete",
                collection=collection_name,
                query=parsed_query,
                multi=multi
            )
            
            logger.info(f"MongoDB DELETE completed successfully. Deleted {result.deleted_count} document(s)")
            return NodeResult(result=result_data)
                
        except Exception as e:
            error_message = DeleteOperation.format_error(e, "DELETE")
            logger.error(f"MongoDB DELETE operation failed: {error_message}")
            return NodeResult(error=error_message)
