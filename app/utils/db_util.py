from typing import Optional

def db_session(session_param_name: str = "db"):
    """Inject database session into method parameters"""
    def decorator(func):
        from functools import wraps
        from app.core.database import get_db
        
        @wraps(func)
        async def wrapper(*args, **kwargs):
            if session_param_name not in kwargs:
                async for db in get_db():
                    kwargs[session_param_name] = db
                    return await func(*args, **kwargs)
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def mongo_db(collection_name: Optional[str] = None, param_name: str = "db"):
    """
    Inject MongoDB database or collection into method parameters
    
    Args:
        collection_name: Optional name of collection. If provided, injects collection instead of db
        param_name: Name of the parameter to inject the database/collection into
    """
    def decorator(func):
        from functools import wraps
        from app.core.database import get_mongodb
        
        @wraps(func)
        async def wrapper(*args, **kwargs):
            if param_name not in kwargs:
                db = await get_mongodb()
                if collection_name:
                    kwargs[param_name] = db[collection_name]
                else:
                    kwargs[param_name] = db
                return await func(*args, **kwargs)
            return await func(*args, **kwargs)
        return wrapper
    return decorator
