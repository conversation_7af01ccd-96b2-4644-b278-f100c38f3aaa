[{"name": "event_type", "display_name": "Event Type", "description": "Type of event", "field_type": "string", "validation_rules": {"required": true, "unique": false, "min_length": 1, "max_length": 100, "min_value": null, "max_value": null, "pattern": null, "enum_values": null}, "default_value": null, "is_default_field": true, "order": 1}, {"name": "data", "display_name": "Data", "description": "JSON object containing event data", "field_type": "object", "validation_rules": {"required": true, "unique": false, "min_length": null, "max_length": null, "min_value": null, "max_value": null, "pattern": null, "enum_values": null}, "default_value": "{}", "is_default_field": true, "order": 2}, {"name": "source", "display_name": "Source", "description": "Source of the event", "field_type": "string", "validation_rules": {"required": true, "unique": false, "min_length": 1, "max_length": 100, "min_value": null, "max_value": null, "pattern": null, "enum_values": null}, "default_value": null, "is_default_field": true, "order": 3}, {"name": "contact_id", "display_name": "Contact ID", "description": "ID of the associated contact", "field_type": "string", "validation_rules": {"required": false, "unique": false, "min_length": null, "max_length": null, "min_value": null, "max_value": null, "pattern": null, "enum_values": null}, "default_value": null, "is_default_field": true, "order": 4}, {"name": "correlation_id", "display_name": "Correlation ID", "description": "ID to correlate related events", "field_type": "string", "validation_rules": {"required": false, "unique": false, "min_length": null, "max_length": null, "min_value": null, "max_value": null, "pattern": null, "enum_values": null}, "default_value": null, "is_default_field": true, "order": 4}, {"name": "status", "display_name": "Status", "description": "Current status of the event", "field_type": "string", "validation_rules": {"required": true, "unique": false, "min_length": null, "max_length": null, "min_value": null, "max_value": null, "pattern": null, "enum_values": ["in_progress", "valid", "validation_failed", "workflow_triggered"]}, "default_value": "pending", "is_default_field": true, "order": 5}, {"name": "validation_errors", "display_name": "Validation Errors", "description": "List of validation errors", "field_type": "array", "validation_rules": {"required": false, "unique": false, "min_length": null, "max_length": null, "min_value": null, "max_value": null, "pattern": null, "enum_values": null}, "default_value": "[]", "is_default_field": true, "order": 6}]