from contextlib import asynccontextmanager
from typing import Any, Dict, Optional
from mcp.server.fastmcp import FastMCP
# from app.core.config import settings
from app.core.database import init_db
from app.credential.utils.credential_registry import CredentialRegistry
from app.node.node_utils.registry import node_registry
from app.repositories.credential_repository import CredentialRepository
from app.schemas.workflow import WorkflowExecuteRequest
from app.utils.db_util import db_session
from app.utils.exceptions import ValidationError
from app.utils.logging import configure_logging, get_logger, log_error
from sqlalchemy.ext.asyncio import AsyncSession

@asynccontextmanager
async def lifespan(mcp: FastMCP):
    """
    Application lifespan manager for startup and shutdown events.
    """
    # Startup
    print("Starting up MCP Server")
    logger = get_logger("mcp.lifespan")
    
    try:
        # Initialize database
        await init_db()
        logger.info("Database initialized")
        
        # # Connect to external services
        # try:
        #     await connect_to_mongo()
        #     logger.info("Connected to MongoDB")
        # except Exception as e:
        #     logger.warning("MongoDB connection failed", error=str(e))

        # try:
        #     await connect_to_redis()
        #     logger.info("Connected to Redis")
        # except Exception as e:
        #     logger.warning("Redis connection failed", error=str(e))

        # try:
        #     # Connect to RabbitMQ and set up consumer
        #     rabbitmq_consumer = RabbitMQConsumer(settings.RABBITMQ_URL)
        #     await rabbitmq_consumer.start()
        #     await rabbitmq_consumer.setup_consumer("events", process_event)
        #     logger.info("RabbitMQ consumer started")
        # except Exception as e:
        #     logger.warning("RabbitMQ consumer setup failed", error=str(e))

        # try:
        #     # Connect to Temporal service if configured
        #     await connect_to_temporal()
        #     logger.info("Connected to Temporal service")
        # except Exception as e:
        #     logger.warning("Temporal service connection failed", error=str(e))
        
        # # Initialize MongoDB collections and indexes
        # await init_mongodb()
        # logger.info("MongoDB initialized")

        # # Create initial data
        # await create_initial_data()
        # logger.info("Initial data created")
        
        # logger.info("Startup completed successfully")
        
    except Exception as e:
        logger.error("Startup failed", error=str(e), exc_info=True)
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down Cerebro API")
    
    try:
        # Stop RabbitMQ consumer
        # if rabbitmq_consumer:
        #     await rabbitmq_consumer.stop()
        #     logger.info("RabbitMQ consumer stopped")
        
        # await close_mongo_connection()
        # logger.info("MongoDB connection closed")
        
        # await close_redis_connection()
        # logger.info("Redis connection closed")

        # await close_temporal_connection()
        # logger.info("Temporal service connection closed")
        
        logger.info("Shutdown completed successfully")
        
    except Exception as e:
        logger.error("Shutdown failed", error=str(e), exc_info=True)

        

mcp = FastMCP("My App", lifespan=lifespan) #host=settings.MCP_HOST, port=settings.MCP_PORT)

app = mcp.streamable_http_app()

@mcp.tool("nodes")
def nodes() -> list[dict]:
    """Get a list of all nodes in cerebro app."""
    nodes: list[dict] = []

    try:
        # Get registered node descriptions
        descriptions = node_registry.list_node_descriptions()
        
        # Use the to_api_format method to properly handle child class properties
        for desc in descriptions:
            nodes.append({
                "name": desc.name,
                "display_name": desc.display_name,
                "description": desc.description,
            })

    except Exception as e:
        raise ValueError(f"Error retrieving nodes: {str(e)}")

    return nodes

@mcp.tool("node_info")
def node_info(node_name: str) -> dict:
    """Get information about a specific node."""
    try:
        # Get the node description from the registry
        node_desc = node_registry.get(node_name)
        
        if not node_desc:
            raise ValueError(f"Node '{node_name}' not found.")
        
        # Convert the description to API format
        return node_desc.get_description().dict()
    
    except Exception as e:
        raise ValueError(f"Error retrieving node info: {str(e)}")


@mcp.tool("credentials")
def credentials() -> list[dict]:
    """Get a list of all credentials."""
    return  CredentialRegistry.get_all_credentials()

@mcp.tool("saved_credentials")
async def saved_credentials(credential_type: str) -> Optional[list[dict]]:
    """Get a list of saved credentials.

    Args:
        credential_type: The type of credential to retrieve (e.g., "whatsapp_api")
    """
    return await _saved_credentials(credential_type)

@db_session()
async def _saved_credentials(credential_type: str, db: Optional[AsyncSession] = None) -> list[dict]:
    """Get a list of saved credentials."""
    if db is None:
        raise ValueError("Database session is required")
    try:
        credential_repo = CredentialRepository(db)
        
        # Get credentials from database
        credentials = await credential_repo.get_by_type(credential_type)
        
        # Return sanitized credential data
        return [credential.to_dict(sanitize_sensitive=True) for credential in credentials]
    except Exception as e:
        raise ValueError(f"Error retrieving saved credentials: {str(e)}")


def validate_node_types(self, nodes: Dict[str, Dict[str, Any]]) -> Optional[bool]:
        """
        Validate that all node types in the workflow are registered and valid.

        Args:
            nodes: Dictionary of nodes with node IDs as keys and node data as values

        Raises:
            ValidationError: If any node type is invalid or not registered
        """
        if not nodes:
            raise ValidationError("Workflow must contain at least one node")

        for node_id, node_data in nodes.items():
            if not isinstance(node_data, dict):
                raise ValidationError(f"Node '{node_id}' data must be a dictionary")

            node_type = node_data.get('type')
            if not node_type:
                raise ValidationError(f"Node '{node_id}' is missing required 'type' field")

            if not isinstance(node_type, str):
                raise ValidationError(f"Node '{node_id}' type must be a string, got {type(node_type).__name__}")

            # Get node class from registry
            node_class = node_registry.get_node_class(node_type)
            if not node_class:
                raise ValidationError(f"Node type '{node_type}' is not registered in the system")

            # Validate the node type by calling the validate method
            try:
                # Create a minimal NodeRequest for validation
                from app.node.node_base.node_models import NodeRequest
                node_request = NodeRequest(
                    type=node_type,
                    parameters=node_data.get('parameters', {}),
                    display_properties=node_data.get('position', {}),
                    name=node_id,
                    is_trigger=node_data.get('is_trigger', False),
                    credentials= node_data.get('credentials', {})
                )
                # Create node instance and validate
                node_instance = node_class()

                if node_request.is_trigger and (node_instance.description.inputs and len(node_instance.description.inputs) != 0):
                    raise ValidationError(f"Node '{node_id}' of type '{node_type}' is not a valid trigger node")

                if not node_request.is_trigger and (node_instance.description.inputs and len(node_instance.description.inputs) == 0):
                    raise ValidationError(f"Node '{node_id}' of type '{node_type}' requires inputs but none are provided")

                validation_result = node_instance.validate(node_request)
                if not validation_result.valid:
                    error_messages = []
                    if validation_result.errors:
                        error_messages = [f"{error.parameter}: {error.message}" for error in validation_result.errors]
                    raise ValidationError(f"Node '{node_id}' of type '{node_type}' validation failed: {'; '.join(error_messages)}")

                return True
            except Exception as e:
                if isinstance(e, ValidationError):
                    raise
                raise ValidationError(f"Node type '{node_type}' validation failed for node '{node_id}': {str(e)}")
 
@mcp.tool("validate_workflow")
def validate_workflow(data: WorkflowExecuteRequest) -> bool:
    """Validate the structure of a workflow.

    Args:
        workflow: The workflow dictionary to validate

    Returns:
        True if the workflow is valid, raises ValueError otherwise
    """
    # if not isinstance(data.work_flow, dict):
    #     raise ValueError("Workflow must be a dictionary")
    if not data.work_flow or not hasattr(data.work_flow, "nodes") or data.work_flow.nodes is None:
            raise ValidationError("Workflow definition (work_flow) with nodes is required for test execution")
    
    nodes_dict = {
            node_id: node.model_dump() if hasattr(node, "model_dump") else dict(node)
            for node_id, node in data.work_flow.nodes.items()
        }
    
    return validate_workflow(nodes_dict)

