from typing import Optional
from app.credential.base.credential import Authenticat<PERSON>, BearerAuthenticator, Credential
from app.credential.base.credential_model import Auth<PERSON>ethod, CredentialModel
from app.credential.utils.credential_registry import credential_provider
from app.node.node_base.node_models import NodeParameter, PropertyTypes


@credential_provider(name="whatsapp_api")
class WhatsAppCredential(Credential):

    @classmethod
    def get_description(cls) -> CredentialModel:
        return CredentialModel(
            name="whatsapp_api",
            display_name="WhatsApp API",
            description="WhatsApp API credentials for sending messages",
            icon="whatsapp",
            icon_color="#25D366",
            icon_url="https://example.com/icons/whatsapp.png",
            documentation_url="https://developers.facebook.com/docs/whatsapp",
            subtitle="WhatsApp API credentials",
            version=1.0,
            allowed_nodes=["whatsapp"],
            base_url="https://graph.facebook.com/v12.0",
        
        parameters = [
            NodeParameter(
                name="access_token",
                display_name="Access Token",
                description="The access token for WhatsApp API",
                type=PropertyTypes.STRING,
                required=True,
                sensitive=True
            ),
            NodeParameter(
                name="business_id",
                display_name="Business Account ID",
                description="The ID of the WhatsApp business account",
                type=PropertyTypes.STRING,
                required=True
            )

        ]

    )

    def get_auth_methods(self, _: Optional[str] = None) -> Authenticator:
        return BearerAuthenticator(
            test_endpoint="/me",
            validation=lambda response: 'id' in response and response['id'] is not None
        )
