"""
MongoDB Credential Model

This module defines the MongoDB credential model for database connections,
supporting both connection string and individual parameter configurations.
"""

import asyncio
from typing import Any, AsyncGenerator, Dict, Optional
from urllib.parse import quote_plus
from app.credential.base.credential import Authenticator, Credential, CustomAuthenticator
from app.credential.base.credential_model import CredentialModel
from app.credential.utils.credential_registry import credential_provider
from app.node.node_base.node_models import NodeParameter, NodeParameterOption, PropertyTypes, DisplayOptions
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from pymongo.errors import (
    ConnectionFailure, 
    ServerSelectionTimeoutError, 
    OperationFailure
)


@credential_provider(name="mongodb")
class MongoDBCredential(Credential):
    """
    MongoDB credential model supporting both connection string and individual parameters.
    
    Provides flexible authentication options for MongoDB connections including:
    - Direct connection string configuration
    - Individual parameter configuration (host, port, database, username, password)
    - SSL/TLS connection options
    - Authentication database specification
    """
    @classmethod
    def get_description(cls) -> CredentialModel:
        return CredentialModel(
            name="mongodb",
            display_name="MongoDB",
            description="MongoDB database credentials for connecting to MongoDB instances",
            icon="mongodb",
            icon_color="#47A248",
            icon_url="https://example.com/icons/mongodb.png",
            documentation_url="https://docs.mongodb.com/manual/reference/connection-string/",
            subtitle="MongoDB database credentials",
            version=1.0,
            allowed_nodes=["mongodb"],
            base_url="",
    
        parameters = [
            # Configuration type selection
            NodeParameter(
                name="configuration_type",
                display_name="Configuration Type",
                description="Choose how to configure the MongoDB connection",
                type=PropertyTypes.OPTIONS,
                required=True,
                default="connection_string",
                options=[
                    NodeParameterOption(name="Connection String", value="connection_string", description="Use a MongoDB connection string"),
                    NodeParameterOption(name="Individual Parameters", value="parameters", description="Configure connection using individual parameters")
                ]
            ),
            
            # Connection string configuration
            NodeParameter(
                name="connection_string",
                display_name="Connection String",
                description="MongoDB connection string (e.g., ********************************:port/database)",
                type=PropertyTypes.STRING,
                required=True,
                sensitive=True,
                placeholder="****************************************************",
                display_options=DisplayOptions(
                    show={"configuration_type": ["connection_string"]}
                )
            ),
            
            # Individual parameter configuration
            NodeParameter(
                name="host",
                display_name="Host",
                description="MongoDB server hostname or IP address",
                type=PropertyTypes.STRING,
                required=True,
                default="localhost",
                placeholder="localhost",
                display_options=DisplayOptions(
                    show={"configuration_type": ["parameters"]}
                )
            ),
            
            NodeParameter(
                name="port",
                display_name="Port",
                description="MongoDB server port number",
                type=PropertyTypes.NUMBER,
                required=True,
                default=27017,
                placeholder="27017",
                display_options=DisplayOptions(
                    show={"configuration_type": ["parameters"]}
                )
            ),
            
            NodeParameter(
                name="database",
                display_name="Database",
                description="MongoDB database name",
                type=PropertyTypes.STRING,
                required=True,
                placeholder="mydatabase",
                display_options=DisplayOptions(
                    show={"configuration_type": ["parameters"]}
                )
            ),
            
            NodeParameter(
                name="username",
                display_name="Username",
                description="MongoDB username for authentication",
                type=PropertyTypes.STRING,
                required=True,
                placeholder="username",
                display_options=DisplayOptions(
                    show={"configuration_type": ["parameters"]}
                )
            ),
            
            NodeParameter(
                name="password",
                display_name="Password",
                description="MongoDB password for authentication",
                type=PropertyTypes.STRING,
                required=True,
                sensitive=True,
                placeholder="password",
                display_options=DisplayOptions(
                    show={"configuration_type": ["parameters"]}
                )
            ),
            
            NodeParameter(
                name="auth_database",
                display_name="Authentication Database",
                description="Database to authenticate against (defaults to admin)",
                type=PropertyTypes.STRING,
                required=True,
                default="admin",
                placeholder="admin",
                display_options=DisplayOptions(
                    show={"configuration_type": ["parameters"]}
                )
            ),
            
            # SSL/TLS options
            NodeParameter(
                name="use_ssl",
                display_name="Use SSL/TLS",
                description="Enable SSL/TLS connection encryption",
                type=PropertyTypes.BOOLEAN,
                required=False,
                default=False,
                display_options=DisplayOptions(
                    show={"configuration_type": ["parameters"]}
                )
            ),
            
            # Connection options
            NodeParameter(
                name="connection_timeout",
                display_name="Connection Timeout (ms)",
                description="Connection timeout in milliseconds",
                type=PropertyTypes.NUMBER,
                required=False,
                default=30000,
                placeholder="30000",
                display_options=DisplayOptions(
                    show={"configuration_type": ["parameters"]}
                )
            )
        ]
    )

    @staticmethod
    def _build_connection_string(credentials: Dict[str, Any]) -> str:
        """
        Build MongoDB connection string from credentials.
        
        Args:
            credentials: Credential data containing connection parameters
            
        Returns:
            str: MongoDB connection string
            
        Raises:
            ValueError: If required credentials are missing or invalid
        """
        config_type = credentials.get('configuration_type', 'connection_string')
        
        if config_type == 'connection_string':
            connection_string = credentials.get('connection_string')
            if not connection_string:
                raise ValueError("Connection string is required when using connection_string configuration")
            return connection_string
        
        elif config_type == 'parameters':
            # Build connection string from individual parameters
            host = credentials.get('host', 'localhost')
            port = credentials.get('port', 27017)
            database = credentials.get('database')
            username = credentials.get('username')
            password = credentials.get('password')
            auth_database = credentials.get('auth_database', 'admin')
            use_ssl = credentials.get('use_ssl', False)
            
            if not database:
                raise ValueError("Database name is required")
            
            # Build connection string
            if username and password:
                # URL encode username and password to handle special characters
                encoded_username = quote_plus(username)
                encoded_password = quote_plus(password)
                connection_string = f"mongodb://{encoded_username}:{encoded_password}@{host}:{port}/{database}"
                
                # Add authentication database if different from target database
                if auth_database and auth_database != database:
                    connection_string += f"?authSource={auth_database}"
                    
                # Add SSL option if enabled
                if use_ssl:
                    separator = "&" if "?" in connection_string else "?"
                    connection_string += f"{separator}ssl=true"
            else:
                connection_string = f"mongodb://{host}:{port}/{database}"
                if use_ssl:
                    connection_string += "?ssl=true"
            
            return connection_string
        
        else:
            raise ValueError(f"Unsupported configuration type: {config_type}")
    
    @staticmethod
    def _get_database_name(credentials: Dict[str, Any]) -> str:
        """
        Extract database name from credentials.
        
        Args:
            credentials: Credential data
            
        Returns:
            str: Database name
            
        Raises:
            ValueError: If database name cannot be determined
        """
        config_type = credentials.get('configuration_type', 'connection_string')
        if config_type == 'connection_string':
            # For connection string, we'll need to parse it or require it in credentials
            database = credentials.get('database')
            if not database:
                raise ValueError("Database name must be provided when using connection string configuration")
            return database
        
        elif config_type == 'parameters':
            database = credentials.get('database')
            if not database:
                raise ValueError("Database name is required in parameters configuration")
            return database
        
        else:
            raise ValueError(f"Unsupported configuration type: {config_type}")

    
    async def _authentication(self, credential_data: dict) -> AsyncGenerator[AsyncIOMotorDatabase, None]:
        """
        Authenticate using the provided MongoDB credential data.
        
        This method builds the connection string or parameters and returns an AsyncIOMotorClient instance.
        
        Args:
            credential_data (dict): The credential data containing connection details.
        
        Returns:
            AsyncIOMotorClient: An instance of AsyncIOMotorClient connected to the MongoDB database.
        """
        connection_string = self._build_connection_string(credential_data)
        database_name = self._get_database_name(credential_data)

        # Get connection timeout from credentials or use default (5 seconds for faster failure)
        timeout_ms = int(credential_data.get('connection_timeout', 5000))
        client = None
        try:
            # Create MongoDB client with shorter timeouts
            client = AsyncIOMotorClient(
                connection_string,
                serverSelectionTimeoutMS=timeout_ms,
                connectTimeoutMS=timeout_ms,
                socketTimeoutMS=timeout_ms
            )
            
            # Test the connection with explicit timeout to avoid hanging
            try:
                # Use asyncio.wait_for to add a timeout to the ping command
                await asyncio.wait_for(client.admin.command('ping'), timeout=5.0)
            except asyncio.TimeoutError:
                raise ConnectionFailure("MongoDB server ping timed out after 5 seconds. The server may be down or unreachable.")
            except ServerSelectionTimeoutError as e:
                # Improve error message with more diagnostic information
                error_details = str(e)
                # Check for common error patterns
                if "Connection refused" in error_details:
                    raise ConnectionFailure(f"Could not connect to MongoDB server: Connection refused. Is the MongoDB service running on the specified host and port? Details: {error_details}")
                elif "Authentication failed" in error_details:
                    raise ConnectionFailure(f"Could not connect to MongoDB server: Authentication failed. Please check your username and password. Details: {error_details}")
                elif "timed out" in error_details.lower():
                    raise ConnectionFailure(f"Could not connect to MongoDB server: Connection timed out. The server may be unreachable or blocked by a firewall. Details: {error_details}")
                else:
                    raise ConnectionFailure(f"Could not connect to MongoDB server: {error_details}")
            
            # Get database
            database = client[database_name]
            try:
                yield database
            finally:
                if client:
                    client.close()

        except ConnectionFailure as e:
            # Provide a more descriptive error message with connection details (but hide credentials)
            host_info = connection_string.split('@')[-1].split('/')[0] if '@' in connection_string else connection_string.split('/')[2]
            if client:
                client.close()
            raise ConnectionFailure(f"Failed to connect to MongoDB at {host_info}: {str(e)}")
        except Exception as e:
            if client:
                client.close()
            raise

    async def _test_credential(self, credential_data: dict) -> bool:
        """
        Test the MongoDB credential by attempting to connect and ping the database.
        
        Args:
            credential_data (dict): The credential data containing connection details.
        
        Returns:
            bool: True if the connection is successful, False otherwise.
        """
        try:
            async for _ in self._authentication(credential_data):
                # If we can get the database, the connection is successful
                return True
        except (ConnectionFailure, ServerSelectionTimeoutError, OperationFailure) as e:
            # Log the error or handle it as needed
            return False
        except Exception as e:
            return False
        
        return False
    
    def get_auth_methods(self, _: Optional[str] = None) -> Authenticator:
        return CustomAuthenticator(
            authenticate=self._authentication,
            test=self._test_credential
        )
    