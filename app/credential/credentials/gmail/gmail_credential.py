from typing import Optional
from app.credential.base.credential import <PERSON>thenticator, Credential
from app.credential.base.credential_model import Auth<PERSON>ethod, CredentialModel
from app.credential.credentials.gmail.gmail_oauth import Gmail<PERSON><PERSON>2<PERSON>uthenticator, GmailServiceAccountAuthenticator
from app.credential.utils.credential_registry import credential_provider
from app.node.node_base.node_models import DisplayOptions, NodeParameter, NodeParameterOption, PropertyTypes

@credential_provider(name="gmail")
class GmailCredential(Credential):
    """Gmail API credentials for email operations.
    This class provides the description and authentication methods for Gmail API"""
    
    @classmethod
    def get_description(cls) -> CredentialModel:
        return CredentialModel(
            name="gmail",
            display_name="Gmail API",
            description="Gmail API credentials for email operations",
            icon="gmail",
            icon_color="#EA4335",
            icon_url="https://developers.google.com/gmail/images/gmail-api-logo.png",
            documentation_url="https://developers.google.com/gmail/api",
            subtitle="Gmail API OAuth2 credentials",
            version=1.0,
            allowed_nodes=["gmail"],
            base_url="https://gmail.googleapis.com/gmail/v1",
            auth_methods=[AuthMethod.OAUTH2, AuthMethod.SERVICE_ACCOUNT],

    parameters= [
        NodeParameter(
            name="gcp_region",
            display_name="Region",
            description="The Google Cloud Platform region for the Gmail API",
            type=PropertyTypes.OPTIONS,
            required=False,
            default="us-central1",
            display_options=DisplayOptions(
                        show={
                            "auth_method": [AuthMethod.SERVICE_ACCOUNT.value]
                        }
                    ),
            options=[
                # Americas
                NodeParameterOption(name="Americas (Council Bluffs)", value="us-central1"),
                NodeParameterOption(name="Americas (South Carolina)", value="us-east1"),
                NodeParameterOption(name="Americas (Ashburn)", value="us-east4"),
                NodeParameterOption(name="Americas (Columbus)", value="us-east5"),
                NodeParameterOption(name="Americas (Oregon)", value="us-west1"),
                NodeParameterOption(name="Americas (Los Angeles)", value="us-west2"),
                NodeParameterOption(name="Americas (Salt Lake City)", value="us-west3"),
                NodeParameterOption(name="Americas (Las Vegas)", value="us-west4"),
                NodeParameterOption(name="Americas (Dallas)", value="us-south1"),
                NodeParameterOption(name="Americas (Montréal)", value="northamerica-northeast1"),
                NodeParameterOption(name="Americas (Toronto)", value="northamerica-northeast2"),
                NodeParameterOption(name="Americas (São Paulo)", value="southamerica-east1"),
                NodeParameterOption(name="Americas (Santiago)", value="southamerica-west1"),

                # Europe
                NodeParameterOption(name="Europe (St. Ghislain)", value="europe-west1"),
                NodeParameterOption(name="Europe (London)", value="europe-west2"),
                NodeParameterOption(name="Europe (Frankfurt)", value="europe-west3"),
                NodeParameterOption(name="Europe (Eemshaven)", value="europe-west4"),
                NodeParameterOption(name="Europe (Zurich)", value="europe-west6"),
                NodeParameterOption(name="Europe (Milan)", value="europe-west8"),
                NodeParameterOption(name="Europe (Paris)", value="europe-west9"),
                NodeParameterOption(name="Europe (Berlin)", value="europe-west10"),
                NodeParameterOption(name="Europe (Turin)", value="europe-west12"),
                NodeParameterOption(name="Europe (Warsaw)", value="europe-central2"),
                NodeParameterOption(name="Europe (Hamina)", value="europe-north1"),
                NodeParameterOption(name="Europe (Madrid)", value="europe-southwest1"),

                # Asia Pacific
                NodeParameterOption(name="Asia (Taiwan)", value="asia-east1"),
                NodeParameterOption(name="Asia (Hong Kong)", value="asia-east2"),
                NodeParameterOption(name="Asia (Tokyo)", value="asia-northeast1"),
                NodeParameterOption(name="Asia (Osaka)", value="asia-northeast2"),
                NodeParameterOption(name="Asia (Seoul)", value="asia-northeast3"),
                NodeParameterOption(name="Asia (Mumbai)", value="asia-south1"),
                NodeParameterOption(name="Asia (Delhi)", value="asia-south2"),
                NodeParameterOption(name="Asia (Singapore)", value="asia-southeast1"),
                NodeParameterOption(name="Asia (Jakarta)", value="asia-southeast2"),

                # Australia & Africa
                NodeParameterOption(name="Australia (Sydney)", value="australia-southeast1"),
                NodeParameterOption(name="Australia (Melbourne)", value="australia-southeast2"),
                NodeParameterOption(name="Africa (Johannesburg)", value="africa-south1"),

                # Middle East
                NodeParameterOption(name="Middle East (Tel Aviv)", value="me-west1"),
                NodeParameterOption(name="Middle East (Doha)", value="me-central1"),
                NodeParameterOption(name="Middle East (Dammam)", value="me-central2"),
            ]
        ),
        NodeParameter(
            name="service_acc_email",
            display_name="Service Account Email",
            description="Email address of the service account for the Gmail API",
            type=PropertyTypes.STRING,
            required=True,
            placeholder="<EMAIL>",
            display_options=DisplayOptions(
                show={"auth_method": [AuthMethod.SERVICE_ACCOUNT.value]}
            )
        ),
        NodeParameter(
            name="service_acc_private_key",
            display_name="Service Account Private Key",
            description="Private key for the service account",
            type=PropertyTypes.STRING,
            required=True,
            sensitive=True,
            display_options=DisplayOptions(
                show={"auth_method": [AuthMethod.SERVICE_ACCOUNT.value]}
            )
        ),
        NodeParameter(
            name="is_impersonate_user",
            display_name="Impersonate a User",
            description="Whether to impersonate a user",
            type=PropertyTypes.BOOLEAN,
            required=False,
            default=False,
            display_options=DisplayOptions(
                show={"auth_method": [AuthMethod.SERVICE_ACCOUNT.value]}
            )
        ),
        NodeParameter(
            name="impersonate_user_email",
            display_name="Impersonate User Email",
            description="Email address of the user to impersonate",
            type=PropertyTypes.STRING,
            required=True,
            placeholder="<EMAIL>",
            display_options=DisplayOptions(
                show={
                    "auth_method": [AuthMethod.SERVICE_ACCOUNT.value],
                    "is_impersonate_user": [True]
                }
            )
        ),

        NodeParameter(
            name="client_id",
            display_name="Client ID",
            description="OAuth2 client ID from Google Cloud Console",
            type=PropertyTypes.STRING,
            required=True,
            placeholder="*********-abcdefghijklmnop.apps.googleusercontent.com",
            display_options=DisplayOptions(
                show={"auth_method": [AuthMethod.OAUTH2.value]}
            )
        ),
        NodeParameter(
            name="client_secret",
            display_name="Client Secret",
            description="OAuth2 client secret from Google Cloud Console",
            type=PropertyTypes.STRING,
            required=True,
            sensitive=True,
            placeholder="GOCSPX-abcdefghijklmnopqrstuvwxyz",
            display_options=DisplayOptions(
                show={"auth_method": [AuthMethod.OAUTH2.value]}
            )
        ),
        NodeParameter(
            name="google_oauth2_button",
            display_name="Google OAuth2 Button",
            description="Button to initiate Google OAuth2 flow",
            type=PropertyTypes.OAUTH2_BUTTON,
            required=False,
            display_options=DisplayOptions(
                show={"auth_method": [AuthMethod.OAUTH2.value]}
            )
        )
    ]

        )

    def get_auth_methods(self, auth_method: Optional[str] = None) -> Authenticator:
        if auth_method == AuthMethod.OAUTH2.value:
            return GmailOAuth2Authenticator()
        elif auth_method == AuthMethod.SERVICE_ACCOUNT.value:
            return GmailServiceAccountAuthenticator()
        else:
            raise ValueError(f"Unsupported auth method: {auth_method}")