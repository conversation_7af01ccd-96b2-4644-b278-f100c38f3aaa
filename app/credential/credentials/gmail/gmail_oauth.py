from app.credential.base.credential import OAuth2Authenticator, ServiceAccountAuthenticator
from app.credential.base.credential_model import CredentialAuthModel


class GmailOAuth2Authenticator(OAuth2Authenticator):
    
    auth_url = "https://accounts.google.com/o/oauth2/v2/auth"
    token_url = "https://oauth2.googleapis.com/token"
    scope = [
        "https://www.googleapis.com/auth/gmail.readonly",
        "https://www.googleapis.com/auth/gmail.send",
        "https://www.googleapis.com/auth/gmail.modify"
    ]
    state = {
        "credential_type": "gmail"
    }
    query_params = {
        "response_type": "code",
        "access_type": "offline",
        "prompt": "consent"
    }

class GmailServiceAccountAuthenticator(ServiceAccountAuthenticator):
    """Authenticator for Gmail Service Account credentials."""

    token_url = "https://oauth2.googleapis.com/token"

    scope = [
            "https://www.googleapis.com/auth/gmail.send" #TO DO: Multiple scope is not suppporting, need to check
            # "https://www.googleapis.com/auth/gmail.readonly"
            # "https://www.googleapis.com/auth/gmail.modify"
        ]

    grant_type =  "urn:ietf:params:oauth:grant-type:jwt-bearer"
    
    token_query_params = {
        "assertion": "{jwt_token}"
    }