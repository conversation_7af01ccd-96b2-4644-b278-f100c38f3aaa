"""
PostgreSQL Credential Model

This module implements the PostgreSQL credential model for database connections,
providing secure authentication and connection parameter management.
"""

import logging
from typing import As<PERSON><PERSON><PERSON>ator, Dict, Optional

import asyncpg
from asyncpg import Pool
from pydantic import BaseModel, Field

from app.credential.base.credential import Authenticator, Credential, CustomAuthenticator
from app.credential.base.credential_model import C<PERSON>entialModel
from app.credential.utils.credential_registry import credential_provider
from app.node.node_base.node_models import NodeParameter, NodeParameterOption, PropertyTypes, PropertyTypeOptions

# Set up logging
logger = logging.getLogger(__name__)


class PostgreSQLConnectionConfig(BaseModel):
    """Configuration model for PostgreSQL connections."""

    host: str = Field(..., description="Database host")
    port: int = Field(5432, description="Database port")
    database: str = Field(..., description="Database name")
    username: str = Field(..., description="Username")
    password: str = Field(..., description="Password")
    ssl_mode: str = Field("prefer", description="SSL mode")
    connection_timeout: int = Field(30, description="Connection timeout in seconds")
    max_connections: int = Field(10, description="Maximum connections in pool")
    allow_unauthorized_certs: bool = Field(False, description="Allow unauthorized certificates")


class PostgreSQLPoolManager:
    """Manages PostgreSQL connection pools."""
    _pools: Dict[str, Pool] = {}


@credential_provider(name="postgresql")
class PostgreSQLCredential(Credential):
    """
    PostgreSQL database credential model.
    
    Provides secure storage and management of PostgreSQL connection parameters
    including host, port, database, authentication, and SSL configuration.
    """
    @classmethod
    def get_description(cls) -> CredentialModel:
        return CredentialModel(
            name="postgresql",
            display_name="PostgreSQL",
            description="PostgreSQL database connection credentials",
            icon="🐘",
            icon_color="#336791",
            icon_url="https://www.postgresql.org/media/img/about/press/elephant.png",
            documentation_url="https://www.postgresql.org/docs/",
            subtitle="PostgreSQL database credentials",
            version=1.0,
            allowed_nodes=["postgresql"],
            base_url="",
            parameters=[
            NodeParameter(
                name="host",
                display_name="Host",
                description="The hostname or IP address of the PostgreSQL server",
                type=PropertyTypes.STRING,
                required=True,
                default="localhost",
                placeholder="localhost"
            ),
            NodeParameter(
                name="port",
                display_name="Port",
                description="The port number of the PostgreSQL server",
                type=PropertyTypes.NUMBER,
                required=True,
                default=5432,
                type_options=PropertyTypeOptions(
                    min_value=1,
                    max_value=65535,
                    number_precision=0
                )
            ),
            NodeParameter(
                name="database",
                display_name="Database",
                description="The name of the PostgreSQL database to connect to",
                type=PropertyTypes.STRING,
                required=True,
                placeholder="my_database"
            ),
            NodeParameter(
                name="username",
                display_name="Username",
                description="The username for database authentication",
                type=PropertyTypes.STRING,
                required=True,
                placeholder="postgres"
            ),
            NodeParameter(
                name="password",
                display_name="Password",
                description="The password for database authentication",
                type=PropertyTypes.STRING,
                required=True,
                sensitive=True
            ),
            NodeParameter(
                name="ssl_mode",
                display_name="SSL Mode",
                description="SSL connection mode for secure connections",
                type=PropertyTypes.OPTIONS,
                required=False,
                default="prefer",
                options=[
                    NodeParameterOption(name="Disable", value="disable"),
                    NodeParameterOption(name="Allow", value="allow"),
                    NodeParameterOption(name="Prefer", value="prefer"),
                    NodeParameterOption(name="Require", value="require"),
                    NodeParameterOption(name="Verify CA", value="verify-ca"),
                    NodeParameterOption(name="Verify Full", value="verify-full")
                ]
            ),
            NodeParameter(
                name="connection_timeout",
                display_name="Connection Timeout",
                description="Connection timeout in seconds (0 for no timeout)",
                type=PropertyTypes.NUMBER,
                required=False,
                default=30,
                type_options=PropertyTypeOptions(
                    min_value=0,
                    max_value=300,
                    number_precision=0
                )
            ),
            NodeParameter(
                name="max_connections",
                display_name="Max Connections",
                description="Maximum number of connections in the pool",
                type=PropertyTypes.NUMBER,
                required=False,
                default=10,
                type_options=PropertyTypeOptions(
                    min_value=1,
                    max_value=100,
                    number_precision=0
                )
            ),
            NodeParameter(
                name="allow_unauthorized_certs",
                display_name="Allow Unauthorized Certificates",
                description="Allow connections with self-signed or invalid SSL certificates",
                type=PropertyTypes.BOOLEAN,
                required=False,
                default=False
            )
        ]
        )

    @classmethod
    def _get_connection_config(cls, credential_data: dict) -> PostgreSQLConnectionConfig:
        """
        Extract PostgreSQL connection configuration from credential data.

        Args:
            credential_data: Dictionary containing credential parameters

        Returns:
            PostgreSQLConnectionConfig: Connection configuration

        Raises:
            ValueError: If credentials are missing or invalid
        """
        if not credential_data:
            raise ValueError("PostgreSQL credential data is required")

        return PostgreSQLConnectionConfig(
            host=credential_data.get('host', 'localhost'),
            port=int(credential_data.get('port', 5432)),
            database=credential_data.get('database', ''),
            username=credential_data.get('username', ''),
            password=credential_data.get('password', ''),
            ssl_mode=credential_data.get('ssl_mode', 'prefer'),
            connection_timeout=int(credential_data.get('connection_timeout', 30)),
            max_connections=int(credential_data.get('max_connections', 10)),
            allow_unauthorized_certs=bool(credential_data.get('allow_unauthorized_certs', False))
        )
    
    @classmethod
    async def _get_pool(cls, config: PostgreSQLConnectionConfig) -> Pool:
        """
        Get or create a connection pool for the given configuration.

        Args:
            config: PostgreSQL connection configuration

        Returns:
            Pool: AsyncPG connection pool
        """
        # Create a unique key for this configuration
        pool_key = f"{config.host}:{config.port}:{config.database}:{config.username}"

        if pool_key not in PostgreSQLPoolManager._pools or PostgreSQLPoolManager._pools[pool_key].is_closing():
            # Create SSL context based on ssl_mode
            ssl_context = None
            if config.ssl_mode != 'disable':
                import ssl
                ssl_context = ssl.create_default_context()
                if config.allow_unauthorized_certs:
                    ssl_context.check_hostname = False
                    ssl_context.verify_mode = ssl.CERT_NONE

            # Create connection pool
            PostgreSQLPoolManager._pools[pool_key] = await asyncpg.create_pool(
                host=config.host,
                port=config.port,
                database=config.database,
                user=config.username,
                password=config.password,
                ssl=ssl_context,
                min_size=1,
                max_size=config.max_connections,
                command_timeout=config.connection_timeout,
                server_settings={
                    'application_name': 'cerebro_postgresql_node'
                }
            )

            logger.info(f"Created PostgreSQL connection pool for {config.host}:{config.port}/{config.database}")

        return PostgreSQLPoolManager._pools[pool_key]
    
    @classmethod
    async def close_all_pools(cls):
        """Close all connection pools."""
        for pool_key, pool in PostgreSQLPoolManager._pools.items():
            if not pool.is_closing():
                await pool.close()
                logger.info(f"Closed connection pool: {pool_key}")
        PostgreSQLPoolManager._pools.clear()

    async def _authentication(self, credential_data: dict) -> AsyncGenerator[asyncpg.Connection, None]:
        """
        Authenticate using the provided PostgreSQL credential data.

        Args:
            credential_data: Credential data containing connection parameters

        Yields:
            asyncpg.Connection: PostgreSQL database connection
        """
        config = self._get_connection_config(credential_data)
        pool = await self._get_pool(config)

        async with pool.acquire() as connection:
            try:
                yield connection
            except Exception as e:
                logger.error(f"Database operation failed: {str(e)}")
                raise

    async def _test_credential(self, credential_data: dict) -> bool:
        """
        Test the PostgreSQL credential by attempting a simple connection.

        Args:
            credential_data: Credential data containing connection parameters

        Returns:
            bool: True if the credential is valid, False otherwise
        """
        try:
            async for connection in self._authentication(credential_data):
                # Test the connection with a simple query
                await connection.fetchval("SELECT 1")
                return True
        except (asyncpg.PostgresError, asyncpg.ConnectionFailureError, OSError) as e:
            # Log the error or handle it as needed
            logger.error(f"PostgreSQL connection test failed: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error during PostgreSQL connection test: {str(e)}")
            return False

        return False

    def get_auth_methods(self, _: Optional[str] = None) -> Authenticator:
        return CustomAuthenticator(
            authenticate=self._authentication,
            test=self._test_credential,
        )
