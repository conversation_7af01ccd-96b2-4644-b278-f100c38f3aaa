"""
Webhook Credential Implementation

This module defines the webhook credential model for webhook authentication
and configuration following the project's established patterns.
"""

from typing import Optional
from app.credential.base.credential import Authenticator, Credential, CustomAuthenticator
from app.credential.base.credential_model import AuthMethod, CredentialAuthModel, CredentialModel, CredentialTestModel
from app.credential.utils.credential_registry import credential_provider
from app.node.node_base.node_models import DisplayOptions, NodeParameter, PropertyTypes, NodeParameterOption


@credential_provider(name="header_auth")
class HeaderAuthCredential(Credential):
    """
    Webhook authentication credential for securing webhook endpoints.
    
    This credential provides various authentication methods for webhook endpoints
    including basic auth, header auth, and JWT authentication.
    """
    @classmethod
    def get_description(cls) -> CredentialModel:
        return CredentialModel(
            name="header_auth",
            display_name="Header Authentication",
            description="Authentication credentials for Header authentication",
            icon="webhook",
            icon_color="#4A90E2",
            icon_url="https://example.com/icons/webhook.png",
            documentation_url="https://docs.example.com/webhooks",
            subtitle="Webhook authentication credentials - Header Auth",
            version=1.0,
            allowed_nodes=["webhook"],
            base_url="",
            parameters=[
                NodeParameter(
                    name="header_name",
                    display_name="Header Name",
                    description="Name of the authentication header",
                    type=PropertyTypes.STRING,
                    required=True,
                    default="X-API-Key"
                ),
                NodeParameter(
                    name="header_value",
                    display_name="Header Value",
                    description="Value of the authentication header",
                    type=PropertyTypes.STRING,
                    required=True,
                    sensitive=True
                )
            ]
        )

    def get_auth_methods(self, _: Optional[str] = None) -> Authenticator:
        return CustomAuthenticator(
            auth_method=AuthMethod.HEADER,
            authenticate=lambda _: None,
            test=lambda _: self._test_auth(),
        )
    
    async def _test_auth(self) -> bool:
        return True