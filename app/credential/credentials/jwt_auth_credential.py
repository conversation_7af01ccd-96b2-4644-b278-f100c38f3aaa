"""
JWT Auth Credential Implementation

This module defines the JWT auth credential model for authenticating
with external services using JSON Web Tokens.
"""

from typing import Optional

from app.credential.base.credential import Authenticator, Credential, CustomAuthenticator
from app.credential.base.credential_model import <PERSON>th<PERSON>ethod, CredentialModel
from app.credential.utils.credential_registry import credential_provider
from app.node.node_base.node_models import NodeParameter, NodeParameterOption, PropertyTypes

SUPPORTED_ALGORITHMS = {
    "HS256": {"min_key_length": 32},
    "HS384": {"min_key_length": 48},
    "HS512": {"min_key_length": 64},
    "RS256": {"requires_pem": True},
    "RS384": {"requires_pem": True},
    "RS512": {"requires_pem": True}
}
@credential_provider(name="jwt_auth")
class JWTAuthCredential(Credential):
    """
    Webhook authentication credential for securing webhook endpoints.
    
    This credential provides JWT authentication methods.
    """

    @classmethod
    def get_description(cls) -> CredentialModel:
        return CredentialModel(
            name="jwt_auth",
            display_name="JWT Authentication",
            description="Authentication credentials for JWT authentication",
            icon="webhook",
            icon_color="#4A90E2",
            icon_url="https://example.com/icons/webhook.png",
            documentation_url="https://docs.example.com/webhooks",
            subtitle="Webhook authentication credentials - JWT Auth",
            version=1.0,
            allowed_nodes=["webhook"],
            base_url="",
            parameters=[
                NodeParameter(
                    name="jwt_secret",
                    display_name="JWT Secret",
                    description="Secret key for JWT authentication",
                    type=PropertyTypes.STRING,
                    required=True,
                    sensitive=True
                ),
                NodeParameter(
                    name="jwt_algorithm",
                    display_name="JWT Algorithm",
                    description="Algorithm used for JWT authentication",
                    type=PropertyTypes.STRING,
                    required=True,
                    default="HS256",
                    options=[
                        NodeParameterOption(name="HS256", value="HS256"),
                        NodeParameterOption(name="HS384", value="HS384"),
                        NodeParameterOption(name="HS512", value="HS512"),
                        NodeParameterOption(name="RS256", value="RS256"),
                        NodeParameterOption(name="RS384", value="RS384"),
                        NodeParameterOption(name="RS512", value="RS512")
                    ]
                )
            ]
        )

    def get_auth_methods(self, _: Optional[str] = None) -> Authenticator:
        return CustomAuthenticator(
            auth_method=AuthMethod.JWT,
            authenticate=lambda _: None,
            test=self._test_auth,
        )
    
    async def _test_auth(self, credential_data: dict) -> bool:
        """Test the JWT credential by attempting to decode a token."""
        try:
            import jwt
            secret = credential_data.get("jwt_secret", "")
            algorithm = credential_data.get("jwt_algorithm", "HS256")

            if not algorithm or algorithm not in SUPPORTED_ALGORITHMS:
                # logger.error(f"Unsupported JWT algorithm: {algorithm}")
                return False
            if not secret:
                # logger.error("JWT secret cannot be empty")
                return False

        # Validate secret based on algorithm
            if algorithm.startswith("HS"):
                min_key_length = SUPPORTED_ALGORITHMS[algorithm]["min_key_length"]
                if len(secret) < min_key_length:
                    # logger.error(f"JWT secret is too short. Minimum length for {algorithm} is {min_key_length} characters")
                    return False
            elif algorithm.startswith("RS"):
                if not secret.startswith("-----BEGIN") or not secret.endswith("KEY-----"):
                    # logger.error("RSA private key must be in PEM format")
                    return False
            
            # Create a dummy token for testing
            dummy_token = jwt.encode({"test": "validation"}, secret, algorithm=algorithm)
            decoded_payload = jwt.decode(dummy_token, secret, algorithms=[algorithm])
            if decoded_payload.get("test") != "validation":
                # logger.error("Token validation failed: payload mismatch")
                return False
                
            return True
        except Exception:
            return False
