"""
Basic Auth Credential Implementation

This module defines the basic auth credential model for authenticating
with external services using basic authentication.
"""

from typing import Optional
from app.credential.base.credential import Authenticator, Credential, CustomAuthenticator
from app.credential.base.credential_model import <PERSON>thMethod, CredentialAuthModel, CredentialModel, CredentialTestModel
from app.credential.utils.credential_registry import credential_provider
from app.node.node_base.node_models import NodeParameter, PropertyTypes


@credential_provider(name="basic_auth")
class BasicAuthCredential(Credential):

    """
    Basic authentication credential for external services.
    """

    @classmethod
    def get_description(cls) -> CredentialModel:
        return CredentialModel(
            name="basic_auth",
            display_name="Basic Authentication",
            description="Authentication credentials for Basic authentication",
            icon="webhook",
            icon_color="#25D366",
            icon_url="https://example.com/icons/webhook.png",
            documentation_url="https://docs.example.com/webhooks",
            subtitle="Webhook authentication credentials - Basic Auth",
            version=1.0,
            allowed_nodes=["webhook"],
            base_url="",

        parameters = [
            NodeParameter(
                name="username",
                display_name="Username",
                description="Username for basic authentication",
                type=PropertyTypes.STRING,
                required=True,
            ),
            NodeParameter(
                name="password",
                display_name="Password",
                description="Password for basic authentication",
                type=PropertyTypes.STRING,
                required=True,
                sensitive=True,
            )
        ]
    )

    def get_auth_methods(self, _: Optional[str] = None) -> Authenticator:
        return CustomAuthenticator(
            auth_method=AuthMethod.BASIC,
            authenticate=lambda _: None,
            test=lambda _: self._test_auth(),
        )
    
    async def _test_auth(self) -> bool:
        return True
    
