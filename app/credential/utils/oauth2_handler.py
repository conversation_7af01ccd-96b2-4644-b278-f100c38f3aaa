import uuid
from typing import Any, Dict
from app.credential.base.credential import <PERSON>Auth2<PERSON><PERSON>ent<PERSON><PERSON>, Authenticator
from app.credential.base.credential_model import <PERSON>th<PERSON><PERSON>od, CredentialRequestModel

from app.credential.utils.credential_registry import CredentialRegistry


class OAuth2Handler:
    """Handles OAuth2 authentication and credential management."""

    @staticmethod
    def get_authentication_url(credential_request: CredentialRequestModel, authenticator: Authenticator) -> Dict[str, str]:
        """        Returns the OAuth2 authorization URL.
        This method constructs the URL using the authenticator's configuration.
        """
        if not isinstance(authenticator, OAuth2Authenticator):
            raise TypeError("authenticator must be an instance of OAuth2Authenticator")
        try:
            auth_config = authenticator.get_authorization_config()

            if 'client_id' in credential_request.parameters and 'client_id' in auth_config["state"]:
                auth_config["state"]["client_id"] = credential_request.parameters["client_id"]

            if credential_request.id and 'credential_id' in auth_config["state"]:
                auth_config["state"]["credential_id"] = credential_request.id

            if 'client_secret' in credential_request.parameters and 'client_secret' in auth_config["state"]:
                auth_config["state"]["client_secret"] = credential_request.parameters["client_secret"]
        
            if 'client_id' in credential_request.parameters and 'client_id' in auth_config["query_params"] :
                auth_config["query_params"]["client_id"] = credential_request.parameters["client_id"]

            if 'redirect_uri' in credential_request.parameters and 'redirect_url' in  auth_config["state"]:
                auth_config["state"]["redirect_url"] = credential_request.parameters["redirect_uri"]

            import base64
            import json
            # Encode state as base64 if it's a dict
            if isinstance(auth_config["state"], dict):
                auth_config["state"] = base64.b64encode(json.dumps(auth_config["state"]).encode()).decode()
                auth_config["query_params"]["state"] = auth_config["state"]  
            # Generate complete OAuth2 authorization URL

            import urllib.parse
            auth_url = auth_config.get('auth_url', '') + '?' + urllib.parse.urlencode({
                k: v for k, v in auth_config.get("query_params", {}).items()
            })
            # Extract redirect URI if available
            redirect_uri = ''
            if isinstance(auth_config["query_params"], dict) and 'redirect_uri' in auth_config["query_params"]:
                redirect_uri = auth_config["query_params"].get('redirect_uri', '')

            return {
                'authorization_url': auth_url,
            }
        except Exception as e:
            raise ValueError(f"Failed to get authentication URL: {str(e)}"
            )

    @staticmethod
    async def handle_auth_callback(code: str, state: str) -> Dict[str, str]:
       """Handles the OAuth2 callback and exchanges the authorization code for tokens."""
       try:
            # Decode state from base64 if it was encoded
            import base64
            import json

            state_data = json.loads(base64.b64decode(state).decode())
            credential_type = state_data.get('credential_type', '')
            if not credential_type:
                raise ValueError(f"Invalid credential type in state: {credential_type}")
            
            credential = CredentialRegistry.get(credential_type)
            if not credential:
                raise ValueError(f"Credential type '{credential_type}' not found in registry")

            # Get authenticator instance
            authenticator = credential.get_auth_methods(AuthMethod.OAUTH2.value)
            if not isinstance(authenticator, OAuth2Authenticator):
                raise TypeError("authenticator must be an instance of OAuth2Authenticator")

            # Get token exchange configuration
            token_config = authenticator.get_token_exchange_config(code)

            if 'client_id' in state_data and 'client_id' in token_config["token_query_params"]:
                token_config["token_query_params"]["client_id"] = state_data["client_id"]

            if 'client_secret' in state_data and 'client_secret' in token_config["token_query_params"]:
                token_config["token_query_params"]["client_secret"] = state_data["client_secret"]
            # Exchange code for tokens
            from app.utils.http_client import HttpClient, HttpMethod, HttpClientError
            try:
                response = await HttpClient.request(
                    method=HttpMethod.POST,
                    url= token_config["token_url"],
                    data=token_config["token_query_params"]
                    # headers={"Content-Type": "application/x-www-form-urlencoded"}
                )
            except HttpClientError as e:
                raise ValueError(f"Failed to exchange code for tokens: {str(e)}")

            # Process response and store tokens
            token_response = response['json']
            if 'access_token' not in token_response or 'refresh_token' not in token_response:
                raise ValueError("Invalid token response received")
            return {
                "refresh_token": token_response['refresh_token'],
                "redirect_uri": state_data.get('redirect_url', ''),
                "credential_id": state_data.get('credential_id', '')
            }
       except Exception as e:
            raise ValueError(f"Failed to handle auth callback: {str(e)}")

    @staticmethod   
    async def get_access_token(credential_data: Dict[str, Any], authenticator: Authenticator) -> Dict[str, Any]:
        """Retrieves the access token using the provided credential data."""
        if not isinstance(authenticator, OAuth2Authenticator):
            raise TypeError("authenticator must be an instance of OAuth2Authenticator")
        
        try:
            # Get token exchange configuration
            body = {k: v["value"] for k, v in credential_data.items() if k != 'redirect_uri' and k != 'credential_id'}
            body['grant_type'] = 'refresh_token'
            from app.utils.http_client import HttpClient, HttpMethod, HttpClientError
            try:
                if authenticator.token_url is None:
                    raise ValueError("authenticator.token_url cannot be None")
                response = await HttpClient.request(
                    method=HttpMethod.POST,
                    url=authenticator.token_url,
                    data=body
                )
                return response['json']['access_token']
            except HttpClientError as e:
                raise ValueError(f"Failed to exchange code for tokens: {str(e)}")
        except Exception as e:
            raise ValueError(f"Failed to get access token: {str(e)}")

