from typing import Any, Callable, Optional, Type, List

from app.credential.base.credential import Credential


class CredentialRegistry:
    
    _credentials: dict[str, Type['Credential']] = {}

    @classmethod
    def register(cls, credential_type: str, credential: Type['Credential']) -> None:
        cls._credentials[credential_type] = credential

    @classmethod
    def get(cls, credential_type: str) -> Optional[Credential]:
        from app.credential.credentials.gmail.gmail_credential import GmailCredential
        from app.credential.credentials.whatsapp_credential import WhatsAppCredential
        from app.credential.credentials.postgresql_credential import PostgreSQLCredential
        from app.credential.credentials.mongodb_credential import MongoDBCredential
        from app.credential.credentials.jwt_auth_credential import JWTAuthCredential
        from app.credential.credentials.header_auth_credential import HeaderAuthCredential
        from app.credential.credentials.basic_auth_credential import BasicAuthCredential

        # from app.credential.credentials.webhook_credential import WebhookCredential

        return cls._credentials[credential_type]()

    @classmethod
    def get_all_credentials(cls) -> List[Any]:
        from app.credential.credentials.whatsapp_credential import WhatsAppCredential
        from app.credential.credentials.postgresql_credential import PostgreSQLCredential
        from app.credential.credentials.mongodb_credential import MongoDBCredential
        from app.credential.credentials.gmail.gmail_credential import GmailCredential
        from app.credential.credentials.jwt_auth_credential import JWTAuthCredential
        from app.credential.credentials.header_auth_credential import HeaderAuthCredential
        from app.credential.credentials.basic_auth_credential import BasicAuthCredential
        # from app.credential.credentials.webhook_credential import WebhookCredential
        
        return [credential.get_description().dict() for credential in cls._credentials.values()]

def credential_provider(name:str) -> Callable:

    def decorator(cls: Type[Credential]) -> Type[Credential]:
        CredentialRegistry.register(name, cls)
        return cls
    return decorator