import uuid
from typing import Any, Dict
from app.credential.base.credential import ServiceAccountAuthenticator, Authenticator


class ServiceAccountAuthHandler:
    """Handles service account authentication and credential management."""

    @staticmethod
    def generate_jwt_token(credential_data: Dict[str, Any], authenticator: Authenticator) -> Dict[str, str]:
        """
        Generates a JWT token for service account authentication.
        This method uses the credential data to create a JWT token.
        """
        if not isinstance(authenticator, ServiceAccountAuthenticator):
            raise TypeError("authenticator must be an instance of ServiceAccountAuthenticator")
        try:
            jwt_config = authenticator.get_jwt_configuration()
            # Replace placeholder in JWT config
            if 'service_acc_email' in credential_data and '{service_acc_email}' in jwt_config['query_params']['iss']:
                service_acc_email = credential_data.get('service_acc_email')
                if not service_acc_email:
                    raise ValueError("Service account email is required but not provided in credential data")
                jwt_config['query_params']['iss'] = jwt_config['query_params']['iss'].replace('{service_acc_email}', service_acc_email)
            
            # Add impersonation if required
            is_impersonate = credential_data.get('is_impersonate_user', False)
            impersonate_email = credential_data.get('impersonate_user_email')
            if is_impersonate and impersonate_email:
                jwt_config['query_params']['sub'] = impersonate_email
            
            import base64
            import jwt
            # Clean up private key format
            service_acc_private_key = credential_data.get('service_acc_private_key')
            if not service_acc_private_key:
                raise ValueError("Service account private key is required but not provided")
                
            private_key = service_acc_private_key
            if not private_key.startswith('-----BEGIN'):
                # Handle base64 encoded keys
                try:
                    private_key = base64.b64decode(private_key).decode('utf-8')
                except Exception as e:
                    raise ValueError(f"Failed to decode private key: {str(e)}")
            
            # Create JWT using the updated config
            jwt_token = jwt.encode(jwt_config['query_params'], private_key, algorithm='RS256')
            return {"jwt_token": jwt_token}
            
        except Exception as e:
            raise ValueError(f"Failed to get JWT configuration: {str(e)}")    

    @staticmethod
    async def get_access_token(credential_data: Dict[str, Any], is_force_refresh: bool, authenticator: Authenticator) -> Dict[str, Any]:
        """
        Retrieves the access token using the JWT token.
        This method uses the authenticator to exchange the JWT for an access token.
        """
        if not isinstance(authenticator, ServiceAccountAuthenticator):
            raise TypeError("authenticator must be an instance of ServiceAccountAuthenticator")
        try:
            credential_data = {k: v["value"] for k, v in credential_data.items()}
            # Check if JWT token exists and is not expired
            jwt_needs_refresh = False
            if 'jwt_token' in credential_data and not is_force_refresh:
                import jwt
                import time
                try:
                    # Decode JWT without verification to check expiration
                    token_data = jwt.decode(credential_data['jwt_token'], options={"verify_signature": False})
                    # Check if token has an expiry and if it's still valid (with 30s buffer)
                    if 'exp' in token_data and token_data['exp'] > time.time() + 30:
                        jwt_needs_refresh = False
                except jwt.PyJWTError:
                    # If there's any issue with the token, we'll regenerate it
                    jwt_needs_refresh = True
            
            if 'jwt_token' not in credential_data or is_force_refresh or jwt_needs_refresh:
                jwt_token = ServiceAccountAuthHandler.generate_jwt_token(credential_data, authenticator)
                credential_data['jwt_token'] = jwt_token['jwt_token']

            access_token_config = authenticator.access_token_config()
            if 'jwt_token' in credential_data and 'assertion' in access_token_config['token_query_params']:
                access_token_config['token_query_params']['assertion'] = credential_data["jwt_token"]
            else:
                raise ValueError("JWT token is required for access token exchange")
            # Ensure the token URL is set
            if not access_token_config.get('token_url'):
                raise ValueError("Token URL is not set in the authenticator configuration")
            from app.utils.http_client import HttpClient, HttpMethod, HttpClientError
            try:
                if authenticator.token_url is None:
                    raise ValueError("authenticator.token_url cannot be None")
                response = await HttpClient.request(
                    method=HttpMethod.POST,
                    url=authenticator.token_url,
                    data=access_token_config['token_query_params'],
                    headers={"Content-Type": "application/x-www-form-urlencoded"}
                )
                return {
                    "access_token": response['json']['access_token'],
                    "jwt_token": credential_data['jwt_token'],
                }
                # return response['json']['access_token']
            except HttpClientError as e:
                raise ValueError(f"Failed to exchange code for tokens: {str(e)}")
        except Exception as e:
            raise ValueError(f"Failed to get access token: {str(e)}")