from contextlib import asynccontextmanager
from typing import Any, Dict, <PERSON>, cast
import re
import uuid
from typing import Optional

from app.core.database import AsyncSessionLocal
from app.core.security import decrypt_credential_data, encrypt_credential_data
from app.credential.base.credential import Bear<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CustomAuthenticator
from app.credential.base.credential_model import <PERSON>th<PERSON>ethod, CredentialRequestModel, CredentialUpdateDB
from app.credential.base.credential_model import <PERSON><PERSON><PERSON>eth<PERSON>, CredentialRequestModel, CredentialUpdateDB
from app.credential.utils.credential_registry import CredentialRegistry
from sqlalchemy.ext.asyncio import AsyncSession

from app.credential.utils.oauth2_handler import <PERSON>Auth2<PERSON><PERSON>ler
from app.credential.utils.service_account_auth_handler import ServiceAccountAuthHandler
from app.models.credential import Credential, CredentialStatus
from app.repositories.credential_repository import CredentialRepository


class CredentialManager:

    def __init__(self, db: Optional[AsyncSession] = None):
        self.db = db
    @asynccontextmanager
    async def _get_session(self):
        """
        Get a database session as an async context manager.
        Uses existing session if available, otherwise creates a new one.
        """
        if self.db is not None:
            yield self.db
        else:
            async with AsyncSessionLocal() as session:
                try:
                    yield session
                finally:
                    await session.close()

    def validate_credential(self, credential_request: CredentialRequestModel):
        credential_class = CredentialRegistry.get(credential_request.type)
        if not credential_class:
            raise ValueError(f"Credential type '{credential_request.type}' not found")

        credential = credential_class.get_description()

        if not credential.parameters:
            raise ValueError(f"Credential type '{credential_request.type}' has no parameters defined")
     
        for param in credential.parameters:
            # Check if required parameter is present, considering display conditions
            if param.required and param.name not in credential_request.parameters:
                # Skip validation if this parameter is conditionally displayed and condition isn't met
                should_validate = True
                
                # Check display options conditions
                if param.display_options and hasattr(param.display_options, 'show'):
                    should_validate = False
                    
                    # For each condition in display_options.show
                    if param.display_options.show is not None:
                        for condition_field, condition_values in param.display_options.show.items():
                            if condition_field == "auth_method":
                            #     # Check if the auth_method matches any of the condition_values
                                if credential_request.auth_method is not None and credential_request.auth_method.value in condition_values:
                                    should_validate = True
                                    break

                            # Check if the condition field is in the request parameters
                            if condition_field in credential_request.parameters:
                                # Check if the value of condition_field matches any of the condition_values
                                if credential_request.parameters[condition_field] in condition_values:
                                    should_validate = True
                                    break
        
                # Only raise error if the parameter should be validated based on display conditions
                if should_validate:
                    raise ValueError(f"Missing required parameter: {param.name}")
                
            # If parameter is present, validate its type
            if param.name in credential_request.parameters:
                value = credential_request.parameters[param.name]
                
                # Validate type based on PropertyTypes enum
                self._validate_param_type(param.name, value, param.type)
                
        return True
    
    async def save_credential(self, credential_request: Union[CredentialRequestModel, CredentialUpdateDB], created_by: int) -> Dict[str, Any]:
        """
        Save a new credential to the database.
        
        Args:
            credential_request: The credential request model with all required parameters
            
        Returns:
            Dictionary with credential information and status
        """
        if not self.db:
            raise ValueError("Database session is required for saving credentials")
        
        credential_repo = CredentialRepository(self.db)
        id = credential_request.id
        # If ID is provided, update existing credential
        if id and isinstance(credential_request, CredentialUpdateDB):
            if credential_request.redirect_uri:
                if credential_request.parameters is None:
                    credential_request.parameters = {}
                credential_request.parameters["redirect_uri"] = credential_request.redirect_uri
                credential_request.redirect_uri = None  # Clear the redirect_uri after moving it
            result = await self.update_credential(credential_request, created_by)

        elif isinstance(credential_request, CredentialRequestModel):
            # Add credential to database
            credential_request.parameters["redirect_uri"] = credential_request.redirect_uri
            credential_request.redirect_uri = None # Clear the redirect_uri after moving it 
            result = await credential_repo.add_credential(
                credential_request=credential_request,
                created_by=created_by
            )
        else:
            raise ValueError("Invalid credential request type")
        
        if not result:
            raise ValueError("Failed to save credential")
        
        if result.auth_method == AuthMethod.OAUTH2.value:
            cred_model = CredentialRegistry.get(result.type)
            if not cred_model:
                raise ValueError(f"Credential type '{result.type}' not found.")
            credential_data = decrypt_credential_data(result.data)
            # Create a credential request model from the decrypted data
            credential_request = CredentialRequestModel(
                id=str(result.id),
                type=result.type,
                name=result.name,
                auth_method=AuthMethod.OAUTH2,
                parameters={k: v["value"] for k, v in credential_data.items()},
            )
            # Validate the credential request
            self.validate_credential(credential_request)
            data = OAuth2Handler.get_authentication_url(credential_request, cred_model.get_auth_methods(AuthMethod.OAUTH2.value))
            data['credential_id'] = str(credential_request.id)
            data['credential_id'] = str(credential_request.id)
            return {
                "status": "success",
                "message": "Credential saved successfully",
                "credential": data
            }
        return {}
    
    async def test_credential(self, id: uuid.UUID):
        if not self.db:
            raise ValueError("Database session is required for credential testing")
        credential_repo = CredentialRepository(self.db)
        credential = await credential_repo.get_by_id(id)
        if not credential:
            raise ValueError(f"Credential with ID {id} not found.")
        
        credential_details = CredentialRegistry.get(credential.type)
        if not credential_details:
            raise ValueError(f"Credential type '{credential.type}' not found.")
        from app.utils.http_client import HttpClient, HttpClientError
        try:
            credential_data = decrypt_credential_data(credential.data)
            auth_method = credential.auth_method
            if auth_method == AuthMethod.SERVICE_ACCOUNT.value:
                is_force_refresh = True
                access_token = await ServiceAccountAuthHandler.get_access_token(credential_data, is_force_refresh, credential_details.get_auth_methods(auth_method))
                if not access_token:
                    raise ValueError("Failed to retrieve access token from JWT")
                
                cred_data = {
                    "jwt_token": access_token.get("jwt_token", "")}
                encrypted_data = self.update_credential_data(credential.data, cred_data)
                await credential_repo.update(credential, {"status": "active", "data": encrypted_data})
                await self.db.commit()
                return True
            elif auth_method == AuthMethod.BEARER.value:
                authenticator = cast(BearerAuthenticator,credential_details.get_auth_methods(auth_method))
                test_config = authenticator.test_credential()
                if not test_config:
                    raise ValueError("Failed to retrieve test configuration")
                url = credential_details.get_description().base_url + test_config.endpoint
                auth = authenticator.authenticate()
                headers = self._get_placeholder_values(auth.headers, credential_data)
                query_params = self._get_placeholder_values(auth.query_params, credential_data)

                response = await HttpClient.request(
                    method=test_config.method,
                    url=url,
                    headers=headers,
                    params=query_params,
                    data=test_config.body
                )

                if test_config.validation is not None:
                    if not test_config.validation(response["json"]):
                        raise ValueError("Credential test failed: Invalid response data.")
            
                if credential.status != "active":
                    await credential_repo.update(credential, {"status": "active"})
                    await self.db.commit()
                    return True
            else:
                credential_data = {k: v["value"] for k, v in credential_data.items()} 
                auth = cast(CustomAuthenticator, credential_details.get_auth_methods(auth_method))
                return await auth.test(credential_data)
                 
        except HttpClientError as e:
            error_msg = str(e)
            if hasattr(e, 'status_code') and e.status_code == 401:
                error_msg += " - Check if you're providing all required parameters for this API"
            raise ValueError(f"Failed to test credential: {error_msg}")

    async def update_credential(self, credential_request: CredentialUpdateDB, updated_by: int) -> Credential:
        """
        Update an existing credential in the database.
    
        This method:
        1. Validates the credential data
        2. Encrypts sensitive parameters
        3. Updates the credential in the database
        
        Args:
            credential_request: The credential request model with updated parameters
            updated_by: ID of the user performing the update
            
        Returns:
            Updated credential model converted to dictionary
        """
        try:
            if not self.db:
                raise ValueError("Database session is required for updating credentials")
            # Initialize repository
            credential_repo = CredentialRepository(self.db)
            # Check if credential exists
            id = credential_request.id
            if not id:
                raise ValueError("Credential ID is required for updates")
                
            try:
                uuid_obj = uuid.UUID(id)
            except ValueError:
                raise ValueError(f"Invalid credential ID format: {id}")
                
            # Get existing credential
            credential = await credential_repo.get_by_id(uuid_obj)
            if not credential:
                raise ValueError(f"Credential with ID {id} not found.")
            
            # Extract update data
            credential_data = credential_request.model_dump(exclude_unset=True, exclude_none=True)
            credential_data.pop('id', None)  # Remove ID from update data
            credential_data['updated_by'] = updated_by
            credential_data['status'] = CredentialStatus.DRAFT.value
            
            # Handle auth_method if provided
            if credential_request.auth_method:
                credential_data['auth_method'] = credential_request.auth_method.value
            # Encrypt sensitive parameters    
            if 'parameters' in credential_data:
                encrypted_credential_data = self.update_credential_data(credential.data, credential_data['parameters'])
                credential_data['data'] = encrypted_credential_data
                credential_data.pop('parameters', None)
            # Update credential
            result = await credential_repo.update(credential, credential_data)
            await self.db.commit()
            
            return result
        except Exception as e:
            raise ValueError(f"Failed to update credential: {str(e)}")

    async def get_oauth2_callback(self, code: str, state: str) -> Dict[str, Any]:
        """
        Handle OAuth2 callback by identifying credential type and processing token exchange.
        
        Args:
            code: Authorization code from OAuth2 provider
            state: Base64 encoded state parameter containing credential information
            
        Returns:
            dict: Complete credential structure with tokens and user information
        """
        try:
            callback_response = await OAuth2Handler.handle_auth_callback(code, state)
            credential_id = callback_response.get('credential_id', '')
            if not self.db:
                raise ValueError("Database session is required for credential testing")
            credential_repo = CredentialRepository(self.db)
            try:
                credential_uuid = uuid.UUID(credential_id)
            except ValueError:
                raise ValueError(f"Invalid credential ID format: {credential_id}")
            
            credential = await credential_repo.get_by_id(credential_uuid)
            if not credential:
                raise ValueError(f"Credential with ID {credential_id} not found.")
            if not credential:
                raise ValueError(f"Credential with ID {credential_id} not found.")
            encrypted_data = self.update_credential_data(credential.data, callback_response)
            await credential_repo.update(credential, {"status": "active", "data": encrypted_data})
            await self.db.commit()    
            return {
                'status': 'success',
                'tokens': callback_response,
                'message': 'OAuth2 callback processed successfully'
            } #TODO: Redirect to the frontend redirect url
            
        except Exception as e:
            raise ValueError(f"Failed to handle OAuth2 callback: {str(e)}")   

    async def get_credential(self, credential_id: str, node_type: str):
        try:
            id = uuid.UUID(credential_id)
        except ValueError:
            raise ValueError(f"Invalid credential ID format: {credential_id}")
        async with self._get_session() as db:
            credential_repo = CredentialRepository(db)
            credential = await credential_repo.get_by_id_and_node(id, node_type)
            if not credential:
                raise ValueError(f"Credential with ID {credential_id} not found.")
            nested_data = decrypt_credential_data(credential.data)
            flat_data = {}
            for key, details in nested_data.items():
                flat_data[key] = details["value"]
            return flat_data,credential
            
    def _replace_placeholders(self, value: Dict[str, Any], params: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Replace placeholders in a dictionary with values from params.
        
        Args:
            value: Dictionary containing values with placeholders
            params: Dictionary containing parameter values
            
        Returns:
            Dictionary with placeholders replaced
        """
        try:
            result = value.copy()
            for key, val in result.items(): 
                if isinstance(val, str):
                    # Find all placeholders like {access_token} within the string
                    placeholders = re.findall(r"\{(.*?)\}", val)
                    for placeholder in placeholders:
                        if placeholder in params:
                            val = val.replace(f"{{{placeholder}}}", str(params[placeholder]))
                            # raise ValueError(f"Missing value for placeholder: {placeholder}")
                    result[key] = val
                elif isinstance(val, dict):
                    # Recursively replace placeholders in nested dictionaries
                    result[key] = self._replace_placeholders(val, params)
                elif isinstance(val, list):
                    # Recursively replace placeholders in lists
                    result[key] = [self._replace_placeholders(item, params) if isinstance(item, dict) else item for item in val]
                else:
                    # For other types, keep the value as is
                    result[key] = val
            return result
        except Exception as e:
            raise ValueError(f"Error replacing placeholders: {e}")
    
    def _get_placeholder_values(self, placeholder_dict: Dict[str, str], params: Dict[str, Dict[str, Any]]) -> dict[str, str]:
        result = placeholder_dict.copy()
        for key, value in result.items():
            # Find all placeholders like {access_token} within the header value
            placeholders = re.findall(r"{(.*?)}", value)

            # Replace each placeholder with its corresponding value from params
            for placeholder in placeholders:
                if placeholder in params:
                    value = value.replace(f"{{{placeholder}}}", str(params[placeholder]["value"]))
                else:
                    raise ValueError(f"Missing value for placeholder: {placeholder}")
            
            # Update the header with the replaced value
            result[key] = value

        return result
    
    async def auth_request(self, credential_id: str, url: str, node_type: str) -> Dict[str, Any]:
        """
        Authenticate a request using a credential.
        
        Args:
            credential_id: ID of the credential to use
            url: The original URL to make the request to
            
        Returns:
            Dict containing base_url, headers and query_params
        """
        try:
            id = uuid.UUID(credential_id)
        except ValueError:
            raise ValueError(f"Invalid credential ID format: {credential_id}")
        if not self.db:
            raise ValueError("Database session is required for credential testing")
        credential_repo = CredentialRepository(self.db)
        credential = await credential_repo.get_by_id(id)
        if not credential:
            raise ValueError(f"Credential with ID {credential_id} not found.")
        
        credential_details = CredentialRegistry.get(credential.type)
        if not credential_details:
            raise ValueError(f"Credential type '{credential.type}' not found.")

        # Decrypt credential data
        credential_data = decrypt_credential_data(credential.data)

        if credential.auth_method == AuthMethod.OAUTH2.value:
            accesss_token = await OAuth2Handler.get_access_token(credential_data, credential_details.get_auth_methods(AuthMethod.OAUTH2.value))
            if not accesss_token:
                raise ValueError("Failed to retrieve access token from OAuth2")
            # Replace placeholders in the URL with the access token
            return {
                "headers": { "Authorization": f"Bearer {accesss_token}"}
            }
        elif credential.auth_method == AuthMethod.SERVICE_ACCOUNT.value:
            accesss_token = await ServiceAccountAuthHandler.get_access_token(credential_data, False, credential_details.get_auth_methods(AuthMethod.SERVICE_ACCOUNT.value))
            if not accesss_token:
                raise ValueError("Failed to retrieve access token from OAuth2")
            # Replace placeholders in the URL with the access token
            return {
                "headers": { "Authorization": f"Bearer {accesss_token.get('access_token', '')}"}
            }
        elif credential.auth_method == AuthMethod.BEARER.value:
            auth = cast(BearerAuthenticator, credential_details.get_auth_methods(AuthMethod.BEARER.value)).authenticate()

            headers = self._get_placeholder_values(auth.headers, credential_data)
            query_params = self._get_placeholder_values(auth.query_params, credential_data)
            url = self._get_endpoint_placeholder_values(url, credential_data)
            
            return {
                "url": url,
                "headers": headers,
                "query_params": query_params
            }

        return {
            "headers": {},
            "query_params": {}
        }

    def _get_endpoint_placeholder_values(self, endpoint: str, params: Dict[str, Dict[str, Any]]) -> str:
        result = endpoint
            # Find all placeholders like {access_token} within the header value
        placeholders = re.findall(r"{(.*?)}", result)

        # Replace each placeholder with its corresponding value from params
        for placeholder in placeholders:
            if placeholder.startswith("credential.") and placeholder.split(".", 1)[1] in params:
                result = result.replace(f"{{{placeholder}}}", str(params[placeholder.split(".", 1)[1]]["value"]))
            else:
                raise ValueError(f"Missing value for placeholder: {placeholder}")
        return result
    
    def _validate_param_type(self, param_name: str, value: Any, param_type_enum: str) -> None:
        """
        Validate that a parameter value matches the expected type from PropertyTypes enum.
        
        Args:
            param_name: The name of the parameter being validated
            value: The value to validate
            param_type_enum: The PropertyTypes enum value
            
        Raises:
            ValueError: If the value doesn't match the expected type
        """
        from app.node.node_base.node_models import PropertyTypes
        
        # Handle validation based on the type specified in PropertyTypes enum
        if param_type_enum == PropertyTypes.STRING:
            if not isinstance(value, str):
                raise ValueError(f"Parameter '{param_name}' must be a string, got {type(value).__name__}")
                
        elif param_type_enum == PropertyTypes.NUMBER:
            if not isinstance(value, (int, float)):
                raise ValueError(f"Parameter '{param_name}' must be a number, got {type(value).__name__}")
                
        elif param_type_enum == PropertyTypes.BOOLEAN:
            if not isinstance(value, bool):
                raise ValueError(f"Parameter '{param_name}' must be a boolean, got {type(value).__name__}")
                
        elif param_type_enum == PropertyTypes.JSON:
            if not isinstance(value, (dict, list)):
                raise ValueError(f"Parameter '{param_name}' must be a JSON object or array, got {type(value).__name__}")
        
        elif param_type_enum == PropertyTypes.OPTIONS or param_type_enum == PropertyTypes.MULTI_OPTIONS:
            # These are typically string values selected from options
            if not isinstance(value, str) and not (isinstance(value, list) and all(isinstance(item, str) for item in value)):
                raise ValueError(f"Parameter '{param_name}' must be a string or list of strings, got {type(value).__name__}")
        
        # Add more type validations as needed for other PropertyTypes
        # For now, other types will pass validation

    def update_credential_data(self, credential_data: str, updated_credential: Dict[str, Any]) -> str:
        """
        Update credential data by merging with existing data.
        Existing parameters are preserved, new parameters are added.
        
        Args:
            credential_data: Encrypted credential data as string
            new_data: New parameters to add/update
            
        Returns:
            Updated encrypted credential data as string
            
        Raises:
            ValueError: If decryption/encryption fails or invalid data provided
        """
        if not credential_data:
            raise ValueError("Credential data cannot be empty")
        
        if not updated_credential:
            # No new data to merge, return original
            return credential_data
        
        try:
            # Decrypt existing data
            existing_data = decrypt_credential_data(credential_data)
            # Validate existing data structure
            if not isinstance(existing_data, dict):
                raise ValueError("Invalid credential data structure")
            
            # Create updated data by merging new parameters
            updated_data = existing_data.copy()
            
            # Process new parameters more efficiently
            for param_name, param_value in updated_credential.items():
                # Only add if parameter doesn't exist or has changed
                if param_name not in existing_data:
                    updated_data[param_name] = {
                        "value": param_value,
                        "is_sensitive": True
                    }
                elif existing_data[param_name].get("value") != param_value:
                    # Update existing parameter if value has changed
                    updated_data[param_name] = {
                        "value": param_value,
                        "is_sensitive": existing_data[param_name].get("is_sensitive", True)
                    }
            # Only encrypt if data actually changed
            if updated_data != existing_data:
                return encrypt_credential_data(updated_data)
            
            # Return original if no changes
            return credential_data
            
        except Exception as e:
            raise ValueError(f"Failed to update credential data: {str(e)}")

    async def get_custom_authentication(self, credential_id: str, node_type: str) -> Any:
        """
        Get custom authentication data for a specific credential and node type.

        Args:
            credential_id: The ID of the credential to retrieve.
            node_type: The type of the node requesting authentication.

        Returns:
            A dictionary containing the custom authentication data.
        """
        try:
            id = uuid.UUID(credential_id)
        except ValueError:
            raise ValueError(f"Invalid credential ID format: {credential_id}")
        
        async with self._get_session() as db:
            credential_repo = CredentialRepository(db)
            credential = await credential_repo.get_by_id_and_node(id, node_type)
            if not credential:
                raise ValueError(f"Credential with ID {credential_id} not found.")
            credential_data = decrypt_credential_data(credential.data)
            credential_data = {k: v["value"] for k, v in credential_data.items()}

            credential_details = CredentialRegistry.get(credential.type)
            if not credential_details:
                raise ValueError(f"Credential type '{credential.type}' not found.")

            # Get custom authentication method
            auth_method = credential_details.get_auth_methods(credential.auth_method)
            if not auth_method or not isinstance(auth_method, CustomAuthenticator):
                raise ValueError(f"Custom authentication method for credential type '{credential.type}' not found or invalid.")

            # Call the custom authenticate method and return the async generator
            return auth_method.authenticate(credential_data)
        
    async def verify_request_authentication(self, credential_id: str, headers: Dict[str, str], node_type: str) -> bool:
        """
        Verify request authentication for basic, header, and JWT credentials based on the credential and node type.

        Args:
            credential_id: The ID of the credential to retrieve.
            headers: The headers of the request.
            node_type: The type of the node requesting authentication.

        Returns:
            True if the request is authenticated, False otherwise.
        """


        data, credential = await self.get_credential(credential_id, node_type)

        if not credential or not data:
            raise ValueError(f"Credential with ID {credential_id} not found.")
        try:
            if credential.auth_method == AuthMethod.BASIC.value:
                auth_header = headers.get("authorization", "")
                if not auth_header.startswith("Basic "):
                    return False
                
                import base64
                encoded_credentials = auth_header[6:]  # Remove "Basic "
                decoded_credentials = base64.b64decode(encoded_credentials).decode("utf-8")
                username, password = decoded_credentials.split(":", 1)
                
                expected_username = data.get("username", "")
                expected_password = data.get("password", "")
                
                return username == expected_username and password == expected_password
            elif credential.auth_method == AuthMethod.HEADER.value:
                header_name = data.get("header_name", "").lower()
                expected_value = data.get("header_value", "")
                actual_value = headers.get(header_name, "")
                return actual_value == expected_value
            elif credential.auth_method == AuthMethod.JWT.value:
                auth_header = headers.get("authorization", "")
                if not auth_header.startswith("Bearer "):
                    return False
                token = auth_header[7:]  # Remove "Bearer "
                
                try:
                    import jwt
                    secret = data.get("jwt_secret", "")
                    algorithm = data.get("algorithm", "HS256")
                    jwt.decode(token, secret, algorithms=[algorithm])
                    return True
                except Exception:
                    return False
            return False
        except Exception as e:
            raise ValueError(f"Failed to verify request authentication: {str(e)}")
            
            