from abc import abstractmethod
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Union
import warnings

from pydantic import BaseModel

from app.node.node_base.node_models import NodeParameter, NodeParameterValue

# Suppress deprecation warnings from pydantic
warnings.filterwarnings("ignore", category=DeprecationWarning, module="pydantic")

class AuthMethod(Enum):
    BEARER = "Bearer"
    JWT = "JWT"
    HEADER = "Header"
    BASIC = "Basic"
    API_KEY = "API_KEY"
    OAUTH2 = "OAuth2"
    CUSTOM = "Custom"
    SERVICE_ACCOUNT = "Service Account"

class CredentialAuthModel(BaseModel):
    method: AuthMethod
    headers: Dict[str, str] = {}
    endpoint: Optional[str] = None
    query_params: Dict[str, Any] = {}
    encode_fields: List[str] = []

class CredentialTestModel(BaseModel):
    endpoint: str
    method: str = "GET"
    query_params: Dict[str, str] = {}
    body: Optional[Dict[str, str]] = None
    validation: Optional[Callable[[dict], bool]] = None

class CredentialModel(BaseModel):
    name: str = ""
    display_name: str = ""
    description: str = ""
    icon: Optional[str] = None
    icon_color: Optional[str] = None
    icon_url: Optional[str] = None
    documentation_url: Optional[str] = None
    subtitle: Optional[str] = None
    version: Union[float, List[float]] = 1.0
    parameters: Optional[List[NodeParameter]] = None
    allowed_nodes: List[str] = []
    base_url: str = ""
    auth_methods: Optional[List[AuthMethod]] = None


    def dict(self, *args, **kwargs):
        """
        Return dict representation with nulls and empty containers filtered out
        """
        try:
            return super().model_dump(*args, **kwargs, exclude_none=True)
        except AttributeError:
           return super().dict(*args, **kwargs, exclude_none=True)
  
class CredentialRequestModel(BaseModel):
    id: Optional[str] = None
    type: str
    name: str
    parameters: Dict[str, NodeParameterValue] = {}
    auth_method: Optional[AuthMethod] = None
    redirect_uri: Optional[str] = None

class CredentialUpdateModel(BaseModel):
    type: Optional[str] = None
    name: Optional[str] = None
    parameters: Optional[Dict[str, NodeParameterValue]] = None
    auth_method: Optional[AuthMethod] = None
    redirect_uri: Optional[str] = None

class CredentialUpdateDB(CredentialUpdateModel):
    id: str