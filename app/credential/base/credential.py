from abc import ABC, abstractmethod
from collections.abc import <PERSON>wai<PERSON>
from typing import Any, Callable, Dict, List, Optional

from app.core.config import Settings
from app.credential.base.credential_model import AuthMethod, CredentialAuthModel, CredentialModel, CredentialTestModel

class Authenticator(ABC):
    """
    Abstract base class for authenticators.
    This class defines the interface for authentication methods.
    """
    auth_method: AuthMethod
    
class BearerAuthenticator(Authenticator):
    """
    Authenticator for Bearer token authentication.
    This class implements the authenticate method for Bearer token authentication.
    """
    auth_method: AuthMethod = AuthMethod.BEARER
    query_params: dict

    test_endpoint: str
    test_method: str = "GET"
    validation: Callable

    def __init__(self,test_endpoint: str, query_params: dict = {}, test_method: str = "GET", validation: Callable = lambda response: True):
        self.test_endpoint = test_endpoint
        self.query_params = query_params
        self.test_method = test_method
        self.validation = validation

    def authenticate(self) -> CredentialAuthModel:
        return CredentialAuthModel(
            method=AuthMethod.BEARER,
            headers={"Authorization": f"Bearer {{access_token}}"},
            query_params=self.query_params
        )

    def test_credential(self) -> CredentialTestModel:
        return CredentialTestModel(
            endpoint=self.test_endpoint,
            method=self.test_method,
            validation=self.validation
        )

class CustomAuthenticator(Authenticator):
    """
    Authenticator for custom authentication methods.
    This class implements the authenticate method for custom authentication.
    """
    auth_method: AuthMethod

    authenticate: Callable[[Dict[str, Any]], Any]
    test: Callable[[Dict[str, Any]], "Awaitable[bool]"]

    def __init__(self, authenticate: Callable[[Dict[str, Any]], Any], test: Callable[[Dict[str, Any]], "Awaitable[bool]"],auth_method: AuthMethod = AuthMethod.CUSTOM):
        self.authenticate = authenticate
        self.test = test
        self.auth_method = auth_method

class OAuth2Authenticator(Authenticator):
    """
    Authenticator for OAuth2 authentication.
    This class implements the authenticate method for OAuth2 authentication.
    """
    auth_method: AuthMethod = AuthMethod.OAUTH2
    base_url: str = Settings().APP_BASE_URL
    api_version: str = Settings().API_V1_STR
    _redirect_uri: str = f"{base_url}{api_version}/credentials/callback"
    
    # OAuth2 configuration properties
    auth_url: Optional[str] = None
    token_url: Optional[str] = None
    scope: Optional[List[str]] = None
    state: Optional[dict] = None
    query_params: Optional[dict] = None
    token_query_params: dict = {}
    
    # Token storage properties
    access_token: Optional[str] = None
    refresh_token: Optional[str] = None
    token_expiry: Optional[int] = None

    def get_authorization_config(self) -> dict:
        """
        Returns the OAuth2 authorization configuration.
        This method can be overridden by subclasses if needed.
        """
        if self.state:
            self.state["redirect_uri"] = self._redirect_uri
            self.state["client_id"] = "{client_id}"
            self.state["client_secret"] = "{client_secret}"
            self.state["scopes"] = " ".join(self.scope) if self.scope else None
            self.state["credential_id"] = "{credential_id}",
            self.state["redirect_url"] = "{redirect_uri}" #This is the actual redirect uri to be used by the client

        if self.query_params:
            self.query_params["client_id"] = "{client_id}"
            self.query_params["redirect_uri"] = self._redirect_uri
            self.query_params["scope"] = " ".join(self.scope) if self.scope else None

        
        return {
            "auth_url": self.auth_url,
            "state": self.state,
            "query_params": self.query_params,

        }
    
    def get_token_exchange_config(self, code: str) -> dict:
        """
        Returns the configuration for exchanging the authorization code for tokens.
        This method can be overridden by subclasses if needed.
        """
        self.token_query_params["client_id"] = "{client_id}"
        self.token_query_params["client_secret"] = "{client_secret}"
        self.token_query_params["code"] = code
        self.token_query_params["grant_type"] = "authorization_code"
        self.token_query_params["redirect_uri"] = self._redirect_uri

        return {
            "token_url": self.token_url,
            "token_query_params": self.token_query_params  
        }
    
    def get_refresh_token_config(self) -> dict:
        """
        Returns the configuration for refreshing the access token.
        This method can be overridden by subclasses if needed.
        """
        return {
            "token_url": self.token_url,
            "client_id": "{client_id}",
            "client_secret": "{client_secret}",
            "refresh_token": "{refresh_token}",
            "grant_type": "refresh_token",
        }
    

class ServiceAccountAuthenticator(Authenticator):
    """
    Authenticator for Service Account authentication.
    This class implements the authenticate method for Service Account authentication.
    """
    auth_method: AuthMethod = AuthMethod.SERVICE_ACCOUNT
    
    # Service Account configuration properties
    token_url: Optional[str] = None
    scope: Optional[List[str]] = None
    query_params: Optional[dict] = {}
    token_query_params: dict = {}
    grant_type: Optional[str] = None

    # Token storage properties
    access_token: Optional[str] = None
    refresh_token: Optional[str] = None
    token_expiry: Optional[int] = None

    def get_jwt_configuration(self) -> dict:
        """
        Returns the JWT configuration for Service Account authentication.
        This method can be overridden by subclasses if needed.
        """
        from datetime import timedelta, datetime, timezone

        # Initialize query_params if it's None
        if self.query_params is None:
            self.query_params = {}

        # Set up expiration timestamps
        now = datetime.now(timezone.utc)
        now_timestamp = int(now.timestamp())
        exp_timestamp = now_timestamp
        
        if self.token_expiry:
            expiration_time = now + timedelta(hours=self.token_expiry)
            exp_timestamp = int(expiration_time.timestamp())
        else:
            # Default expiration: 1 hour
            expiration_time = now + timedelta(hours=1)
            exp_timestamp = int(expiration_time.timestamp())

        # Make sure we're working with a dict for query_params
        if not isinstance(self.query_params, dict):
            self.query_params = {}
            
        # Set required JWT parameters
        self.query_params["aud"] = self.token_url
        self.query_params["iss"] = "{service_acc_email}"
        if self.scope:
            self.query_params["scope"] = " ".join(self.scope)
        self.query_params["exp"] = exp_timestamp
        self.query_params["iat"] = now_timestamp

        return {
            "token_url": self.token_url,
            "query_params": self.query_params
        }
    
    def access_token_config(self) -> dict:
        """
        Returns the configuration for fetching the access token using JWT.
        This method can be overridden by subclasses if needed.
        """
        if self.token_query_params:
            self.token_query_params["grant_type"] = self.grant_type
        return {
            "token_url": self.token_url,
            "token_query_params": self.token_query_params or {}
        }

class Credential(ABC):
    
    @classmethod
    @abstractmethod
    def get_description(cls) -> CredentialModel:
        raise NotImplementedError("Subclasses must implement this method.")
    
    @abstractmethod
    def get_auth_methods(self, auth_method: Optional[str] = None) -> Authenticator:
        """
        Returns a list of authentication methods supported by the credential.
        """
        raise NotImplementedError("Subclasses must implement this method.")
