"""
MongoDB repository for dynamic schema management.
"""

from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from uuid import UUID

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorDatabase
from pymongo import ASCENDING, IndexModel
from pymongo.errors import DuplicateKeyError

from app.schemas.schema import SchemaDefinitionCreateDB, SchemaDefinitionUpdate
from app.utils.exceptions import ConflictError, NotFoundError, DatabaseError
from app.utils.logging import get_logger

logger = get_logger("repositories.schema")


class SchemaRepository:
    """Repository for dynamic schema MongoDB operations."""
    
    def __init__(self, db: AsyncIOMotorDatabase):
        """
        Initialize repository with MongoDB database.
        
        Args:
            db: MongoDB database instance
        """
        self.db = db
        self.collection = db.schema_definition
    
    async def ensure_indexes(self) -> None:
        """Create necessary indexes for optimal performance."""
        try:
            indexes = [
                IndexModel([("name", ASCENDING)], unique=True),
                IndexModel([("created_by", ASCENDING)]),
                IndexModel([("created_at", ASCENDING)]),
                IndexModel([("updated_at", ASCENDING)]),
                IndexModel([("version", ASCENDING)]),
                IndexModel([("is_system_schema", ASCENDING)]),
            ]
            await self.collection.create_indexes(indexes)
            logger.info("Schema indexes created successfully")
        except Exception as e:
            logger.error(f"Failed to create indexes: {e}")
            raise DatabaseError(f"Failed to create indexes: {e}")
    
    async def create(self, schema_data: SchemaDefinitionCreateDB) -> Dict[str, Any]:
        """
        Create a new schema.
        
        Args:
            schema_data: Schema creation data
            
        Returns:
            Dict[str, Any]: Created schema document
            
        Raises:
            ConflictError: If schema name already exists
            DatabaseError: If database operation fails
        """
        try:
            now = datetime.now(timezone.utc)
            
            # Convert schema fields to dict format
            fields_dict = []
            for field in schema_data.fields:
                field_dict = {
                    "name": field.name,
                    "display_name": field.display_name,
                    "description": field.description,
                    "field_type": field.field_type.value,
                    "validation_rules": field.validation_rules.model_dump(),
                    "default_value": field.default_value,
                    "is_default_field": field.is_default_field,
                    "order": field.order
                }
                fields_dict.append(field_dict)

            document = {
                "name": schema_data.name,
                "display_name": schema_data.display_name,
                "description": schema_data.description,
                "fields": fields_dict,
                "version": 1,
                "created_by": schema_data.created_by,
                "updated_by": None,
                "is_system_schema": False,
                "created_at": now,
                "updated_at": now,
            }
            
            result = await self.collection.insert_one(document)
            
            # Retrieve the created document
            created_doc = await self.collection.find_one({"_id": result.inserted_id})
            if not created_doc:
                raise DatabaseError("Failed to retrieve created schema")
            
            # Convert ObjectId to string
            created_doc["id"] = str(created_doc["_id"])
            del created_doc["_id"]
            
            logger.info(f"Created schema: {schema_data.name}")
            return created_doc
            
        except DuplicateKeyError:
            raise ConflictError(f"Schema with name '{schema_data.name}' already exists")
        except Exception as e:
            logger.error(f"Failed to create schema: {e}")
            raise DatabaseError(f"Failed to create schema: {e}")
    
    async def get_by_id(self, schema_id: str) -> Optional[Dict[str, Any]]:
        """
        Get schema by ID.
        
        Args:
            schema_id: Schema ID (ObjectId as string)
            
        Returns:
            Optional[Dict[str, Any]]: Schema document or None
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            if not ObjectId.is_valid(schema_id):
                return None
            
            document = await self.collection.find_one({"_id": ObjectId(schema_id)})
            if document:
                document["id"] = str(document["_id"])
                del document["_id"]
            
            return document
            
        except Exception as e:
            logger.error(f"Failed to get schema by ID {schema_id}: {e}")
            raise DatabaseError(f"Failed to get schema: {e}")
    
    async def get_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """
        Get schema definitions by name.
        
        Args:
            name: Schema definition name
            
        Returns:
            Optional[Dict[str, Any]]: Schema document or None
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            document = await self.collection.find_one({"name": name})
            if document:
                document["id"] = str(document["_id"])
                del document["_id"]
            
            return document
            
        except Exception as e:
            logger.error(f"Failed to get schema by name {name}: {e}")
            raise DatabaseError(f"Failed to get schema: {e}")
    
    async def get_multi(
        self,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        sort_by: str = "created_at",
        sort_order: int = -1
    ) -> List[Dict[str, Any]]:
        """
        Get multiple schemas with pagination and filtering.
        
        Args:
            skip: Number of documents to skip
            limit: Maximum number of documents to return
            filters: Optional filters to apply
            sort_by: Field to sort by
            sort_order: Sort order (1 for ascending, -1 for descending)
            
        Returns:
            List[Dict[str, Any]]: List of schema documents
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            query = filters or {}
            
            # Convert string filters to proper format
            if "created_by" in query and isinstance(query["created_by"], str):
                try:
                    query["created_by"] = int(query["created_by"])
                except ValueError:
                    logger.warning(f"Invalid created_by value: {query['created_by']}")
                    # Handle invalid value - either remove it or set to None
                    query.pop("created_by", None)
            
            cursor = self.collection.find(query).sort(sort_by, sort_order).skip(skip).limit(limit)
            documents = await cursor.to_list(length=limit)
            
            # Convert ObjectId to string for each document
            for doc in documents:
                doc["id"] = str(doc["_id"])
                del doc["_id"]
            
            return documents
            
        except Exception as e:
            logger.error(f"Failed to get schemas: {e}")
            raise DatabaseError(f"Failed to get schemas: {e}")
    
    async def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """
        Count schemas with optional filtering.
        
        Args:
            filters: Optional filters to apply
            
        Returns:
            int: Number of matching documents
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            query = filters or {}

            # Convert string filters to proper format
            if "created_by" in query and isinstance(query["created_by"], str):
                try:
                    query["created_by"] = int(query["created_by"])
                except ValueError:
                    logger.warning(f"Invalid created_by value: {query['created_by']}")
                    # Handle invalid value - either remove it or set to None
                    query.pop("created_by", None)
            
            return await self.collection.count_documents(query)
            
        except Exception as e:
            logger.error(f"Failed to count schemas: {e}")
            raise DatabaseError(f"Failed to count schemas: {e}")
    
    async def update(self, schema_id: str, update_data: SchemaDefinitionUpdate) -> Optional[Dict[str, Any]]:
        """
        Update an existing schema.
        
        Args:
            schema_id: Schema ID (ObjectId as string)
            update_data: Update data
            
        Returns:
            Optional[Dict[str, Any]]: Updated schema document or None
            
        Raises:
            ConflictError: If name conflict occurs
            DatabaseError: If database operation fails
        """
        try:
            if not ObjectId.is_valid(schema_id):
                return None
            
            # Build update document with fields to set
            set_fields: Dict[str, Any] = {
                "updated_at": datetime.now(timezone.utc),
                "updated_by": update_data.updated_by
            }
            
            if update_data.display_name is not None:
                set_fields["display_name"] = update_data.display_name
            if update_data.description is not None:
                set_fields["description"] = update_data.description
            if update_data.fields is not None:
                # Convert schema fields to dict format
                fields_dict = []
                for field in update_data.fields:
                    field_dict = {
                        "name": field.name,
                        "display_name": field.display_name,
                        "description": field.description,
                        "field_type": field.field_type.value,
                        "validation_rules": field.validation_rules.model_dump(),
                        "default_value": field.default_value,
                        "is_default_field": field.is_default_field,
                        "order": field.order
                    }
                    fields_dict.append(field_dict)
                set_fields["fields"] = fields_dict
            
            # Build update operation
            update_operation = {"$set": set_fields}
            
            # Add increment operation if fields change
            if update_data.fields is not None:
                update_operation["$inc"] = {"version": 1}

            # Perform update
            result = await self.collection.find_one_and_update(
                {"_id": ObjectId(schema_id)},
                update_operation,
                return_document=True
            )
            
            if result:
                result["id"] = str(result["_id"])
                del result["_id"]
            
            return result
            
        except DuplicateKeyError:
            raise ConflictError(f"Schema with name already exists")
        except Exception as e:
            logger.error(f"Failed to update schema {schema_id}: {e}")
            raise DatabaseError(f"Failed to update schema: {e}")
    
    async def delete(self, schema_id: str) -> bool:
        """
        Delete a schema Definition.
        
        Args:
            schema_id: Schema ID (ObjectId as string)
            
        Returns:
            bool: True if deleted, False if not found
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            if not ObjectId.is_valid(schema_id):
                return False
            
            result = await self.collection.delete_one({"_id": ObjectId(schema_id)})
            return result.deleted_count > 0
            
        except Exception as e:
            logger.error(f"Failed to delete schema {schema_id}: {e}")
            raise DatabaseError(f"Failed to delete schema: {e}")
    
    async def exists(self, schema_id: str) -> bool:
        """
        Check if schema exists.
        
        Args:
            schema_id: Schema ID (ObjectId as string)
            
        Returns:
            bool: True if exists, False otherwise
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            if not ObjectId.is_valid(schema_id):
                return False
            
            count = await self.collection.count_documents({"_id": ObjectId(schema_id)}, limit=1)
            return count > 0
            
        except Exception as e:
            logger.error(f"Failed to check schema existence {schema_id}: {e}")
            raise DatabaseError(f"Failed to check schema existence: {e}")
    
    async def replace(self, schema_id: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Replace a schema document (keeping _id, name and system fields).
        
        Args:
            schema_id: Schema ID
            data: New schema data (excluding immutable fields)
            
        Returns:
            Optional[Dict[str, Any]]: Updated document or None
        """
        try:
            # Get existing document to preserve immutable fields
            existing = await self.get_by_id(schema_id)
            if not existing:
                return None
            
            # Preserve immutable fields
            name = existing["name"]
            is_system_schema = existing.get("is_system_schema", False)
            created_at = existing["created_at"]
            created_by = existing.get("created_by")
            
            # Prepare update with current timestamp
            replacement = {
                **data,
                "name": name,  # Preserve name
                "is_system_schema": is_system_schema,  # Preserve system flag
                "created_at": created_at,  # Preserve creation timestamp
                "created_by": created_by,  # Preserve creator
                "updated_at": datetime.utcnow()
            }
            
            # Perform replacement
            result = await self.collection.replace_one(
                {"_id": ObjectId(schema_id)}, 
                replacement
            )
            
            if result.modified_count == 0:
                return None
            
            # Get updated document
            updated = await self.get_by_id(schema_id)
            return updated
            
        except Exception as e:
            logger.error(f"Failed to replace schema {schema_id}: {e}")
            raise DatabaseError(f"Failed to replace schema: {e}")
