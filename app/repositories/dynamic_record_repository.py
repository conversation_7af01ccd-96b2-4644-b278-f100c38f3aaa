"""
MongoDB repository for dynamic record management.
"""

from datetime import datetime, timezone
from typing import Dict, List, Optional, Any

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.schemas.dynamic_record import DynamicRecordCreate, DynamicRecordUpdate
from app.utils.exceptions import DatabaseError
from app.utils.logging import get_logger

logger = get_logger("repositories.dynamic_record")


class DynamicRecordRepository:
    """Repository for dynamic record MongoDB operations."""
    
    def __init__(self, db: AsyncIOMotorDatabase, schema_name: str=""):
        """
        Initialize repository with MongoDB database.
        
        Args:
            db: MongoDB database instance
        """
        self.db = db
        self.schema_name = schema_name

    def _get_collection(self):
        """Get the collection for the current schema."""
        if not self.schema_name:
            raise ValueError("Schema name not set")
        return self.db[self.schema_name]
    
    # async def ensure_indexes(self) -> None:
    #     """Create necessary indexes for optimal performance."""
    #     try:
    #          # If no schema name is set, use a default collection for common indexes
    #         if not self.schema_name:
    #             logger.info("No schema name set, creating common indexes only")
    #             # Create indexes that don't require a specific schema
    #         common_indexes = [
    #             IndexModel([("schema_name", ASCENDING)]),
    #             IndexModel([("_created_on", ASCENDING)]),
    #             IndexModel([("_updated_on", ASCENDING)])
    #         ]
    #         await self.db["dynamic_records_common"].create_indexes(common_indexes)
    #         logger.info("Common dynamic record indexes created successfully")
    #         return
    #         indexes = [
    #             IndexModel([("schema_name", ASCENDING)]),
    #             IndexModel([("_created_on", ASCENDING)]),
    #             IndexModel([("_updated_on", ASCENDING)]),
    #             IndexModel([("schema_name", ASCENDING), ("_created_on", DESCENDING)]),
    #             IndexModel([("schema_name", ASCENDING), ("data.email", ASCENDING)], sparse=True),
    #             IndexModel([("schema_name", ASCENDING), ("data.phone", ASCENDING)], sparse=True),
    #         ]
    #         await self._get_collection().create_indexes(indexes)
    #         logger.info("Dynamic record indexes created successfully")
    #     except Exception as e:
    #         logger.error(f"Failed to create indexes: {e}")
    #         raise DatabaseError(f"Failed to create indexes: {e}")
    
    async def create(self, record_data: DynamicRecordCreate) -> Dict[str, Any]:
        """
        Create a new dynamic record.
        
        Args:
            record_data: Record creation data
            
        Returns:
            Dict[str, Any]: Created record document
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            now = datetime.now(timezone.utc)
            
            document = record_data.data
            document["_created_on"] = now
            document["_updated_on"] = now
            document["_created_by"] = str(record_data.created_by)
            document["_created_by_source"] = record_data.created_by_source.value
            document["_updated_by"] = str(record_data.created_by)
            document["_updated_by_source"] = record_data.created_by_source.value
            
            collection = self.db[record_data.schema_name]
            result = await collection.insert_one(document)
            
            # Retrieve the created document
            created_doc = await collection.find_one({"_id": result.inserted_id})
            if not created_doc:
                raise DatabaseError("Failed to retrieve created record")

            # Convert ObjectId to string and use record_id as id
            created_doc["id"] = str(result.inserted_id)
            del created_doc["_id"]
            
            logger.info(f"Created record in schema: {record_data.schema_name}")
            return created_doc
            
        except Exception as e:
            logger.error(f"Failed to create record: {e}")
            raise DatabaseError(f"Failed to create record: {e}")
    
    async def get_by_id(self, record_id: str) -> Optional[Dict[str, Any]]:
        """
        Get record by ID and schema name.
        
        Args:
            record_id: Record ID
            
        Returns:
            Optional[Dict[str, Any]]: Record document or None
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            document = await self._get_collection().find_one({
                "_id": ObjectId(record_id)
            })
            if document:
                document["id"] = record_id
                del document["_id"]
            return document
            
        except Exception as e:
            logger.error(f"Failed to get record by ID {record_id}: {e}")
            raise DatabaseError(f"Failed to get record: {e}")
    
    async def get_multi(
        self,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[List[Dict[str, Any]]] = None,
        sort_by: str = "_created_on",
        sort_order: int = -1
    ) -> List[Dict[str, Any]]:
        """
        Get multiple records with pagination and filtering.
        
        Args:
            skip: Number of documents to skip
            limit: Maximum number of documents to return
            filters: Optional filters to apply
            sort_by: Field to sort by
            sort_order: Sort order (1 for ascending, -1 for descending)
            
        Returns:
            List[Dict[str, Any]]: List of record documents
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:

            query: Dict[str, Any] = {}

            # Apply filters
            if filters:
                filter_conditions = []
                for filter_item in filters:
                    field = filter_item.get("field")
                    operator = filter_item.get("operator")
                    value = filter_item.get("value")
                    
                    if not all([field, operator, value is not None]):
                        continue
                    
                    # Build MongoDB query based on operator
                    if field is not None and isinstance(field, str):
                        field_path = f"{field}"
                    else:
                        continue

                    if operator == "eq":
                        filter_conditions.append({field_path: value})
                    elif operator == "ne":
                        filter_conditions.append({field_path: {"$ne": value}})
                    elif operator == "gt":
                        filter_conditions.append({field_path: {"$gt": value}})
                    elif operator == "gte":
                        filter_conditions.append({field_path: {"$gte": value}})
                    elif operator == "lt":
                        filter_conditions.append({field_path: {"$lt": value}})
                    elif operator == "lte":
                        filter_conditions.append({field_path: {"$lte": value}})
                    elif operator == "in":
                        if isinstance(value, list):
                            filter_conditions.append({field_path: {"$in": value}})
                    elif operator == "nin":
                        if isinstance(value, list):
                            filter_conditions.append({field_path: {"$nin": value}})
                    elif operator == "contains":
                        filter_conditions.append({field_path: {"$regex": str(value), "$options": "i"}})
                    elif operator == "regex":
                        filter_conditions.append({field_path: {"$regex": str(value)}})
                
                if filter_conditions:
                    query["$and"] = filter_conditions
            
            # Handle sorting
            sort_field = f"{sort_by}"
            cursor = self._get_collection().find(query).sort(sort_field, sort_order).skip(skip).limit(limit)     
            documents = await cursor.to_list(length=limit)
            # Convert documents
            for doc in documents:
                doc["id"] = str(doc["_id"])
                del doc["_id"]
            return documents
            
        except Exception as e:
            logger.error(f"Failed to get records for schema {self.schema_name}: {e}")
            raise DatabaseError(f"Failed to get records: {e}")
    
    async def count(self, filters: Optional[List[Dict[str, Any]]] = None) -> int:
        """
        Count records with optional filtering.
        
        Args:
            filters: Optional filters to apply
            
        Returns:
            int: Number of matching documents
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            query = {}
            
            # Apply filters (same logic as get_multi)
            if filters:
                filter_conditions = []
                for filter_item in filters:
                    field = filter_item.get("field")
                    operator = filter_item.get("operator")
                    value = filter_item.get("value")
                    
                    if not all([field, operator, value is not None]):
                        continue
                    
                    if field is not None and isinstance(field, str):
                        field_path = f"data.{field}" if not field.startswith("data.") else field
                    else:
                        continue
            
                    if operator == "eq":
                        filter_conditions.append({field_path: value})
                    elif operator == "ne":
                        filter_conditions.append({field_path: {"$ne": value}})
                    elif operator == "gt":
                        filter_conditions.append({field_path: {"$gt": value}})
                    elif operator == "gte":
                        filter_conditions.append({field_path: {"$gte": value}})
                    elif operator == "lt":
                        filter_conditions.append({field_path: {"$lt": value}})
                    elif operator == "lte":
                        filter_conditions.append({field_path: {"$lte": value}})
                    elif operator == "in":
                        if isinstance(value, list):
                            filter_conditions.append({field_path: {"$in": value}})
                    elif operator == "nin":
                        if isinstance(value, list):
                            filter_conditions.append({field_path: {"$nin": value}})
                    elif operator == "contains":
                        filter_conditions.append({field_path: {"$regex": str(value), "$options": "i"}})
                    elif operator == "regex":
                        filter_conditions.append({field_path: {"$regex": str(value)}})
                
                if filter_conditions:
                    query["$and"] = filter_conditions
            
            return await self._get_collection().count_documents(query)
            
        except Exception as e:
            logger.error(f"Failed to count records for schema {self.schema_name}: {e}")
            raise DatabaseError(f"Failed to count records: {e}")
    
    async def update(self, record_id: str, update_data: DynamicRecordUpdate) -> Optional[Dict[str, Any]]:
        """
        Update an existing record.
        
        Args:
            record_id: Record ID
            update_data: Update data
            
        Returns:
            Optional[Dict[str, Any]]: Updated record document or None
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            # Build update document
            set_fields = {
                "_updated_on": datetime.now(timezone.utc)
            }
            set_fields.update(update_data.data)
            # Build update operation
            update_operation = {
                "$set": set_fields,
            }

            result = await self._get_collection().find_one_and_update(
                {"_id": ObjectId(record_id)},
                update_operation,
                return_document=True
            )
            
            if result:
                result["id"] = record_id
                del result["_id"]
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to update record {record_id}: {e}")
            raise DatabaseError(f"Failed to update record: {e}")
    
    async def delete(self, record_id: str) -> bool:
        """
        Delete a record.
        
        Args:
            record_id: Record ID
            
        Returns:
            bool: True if deleted, False if not found
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            result = await self._get_collection().delete_one({
               "_id": ObjectId(record_id) })
            return result.deleted_count > 0
            
        except Exception as e:
            logger.error(f"Failed to delete record {record_id}: {e}")
            raise DatabaseError(f"Failed to delete record: {e}")
    
    async def exists(self, record_id: str) -> bool:
        """
        Check if record exists.
        
        Args:
            record_id: Record ID
            
        Returns:
            bool: True if exists, False otherwise
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            # count = await self._get_collection().find_one({
            #     "_id": ObjectId(record_id)
            # })
            count = await self._get_collection().count_documents({
               "_id": ObjectId(record_id) }, limit=1)
            return count > 0
            
        except Exception as e:
            logger.error(f"Failed to check record existence {record_id}: {e}")
            raise DatabaseError(f"Failed to check record existence: {e}")
    
    async def bulk_create(self, records_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Create multiple records in bulk.
        
        Args:
            records_data: List of record data
            
        Returns:
            List[Dict[str, Any]]: List of created record documents
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            collection = self._get_collection()
            now = datetime.now(timezone.utc)
            documents = []
            
            for data in records_data:
                data["_created_on"] = now
                data["_updated_on"] = now
                documents.append(data)
            
            result = await collection.insert_many(documents)
            
            # Retrieve created documents
            created_docs = await collection.find({
                "_id": {"$in": result.inserted_ids}
            }).to_list(length=len(result.inserted_ids))
            
            # Convert documents
            for doc in created_docs:
                doc["id"] = str(doc["_id"])
                del doc["_id"]
            
            logger.info(f"Bulk created {len(created_docs)} records in schema: {self.schema_name}")
            return created_docs
            
        except Exception as e:
            logger.error(f"Failed to bulk create records: {e}")
            raise DatabaseError(f"Failed to bulk create records: {e}")
    
    async def get_schema_stats(self) -> Dict[str, Any]:
        """
        Get statistics for records in a schema.
        
        Args:
            
        Returns:
            Dict[str, Any]: Statistics data
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            today = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
            # Aggregate statistics
            pipeline = [
                # No schema_name match needed since we're already in the correct collection
                {
                    "$group": {
                        "_id": None,
                        "total_records": {"$sum": 1},
                        "created_today": {
                            "$sum": {
                                "$cond": [{"$gte": ["$_created_on", today]}, 1, 0]
                            }
                        },
                        "updated_today": {
                            "$sum": {
                                "$cond": [{"$gte": ["$_updated_on", today]}, 1, 0]
                            }
                        }
                    }
                }
            ]
            result = await self._get_collection().aggregate(pipeline).to_list(length=1)
            if result:
                stats = result[0]
                del stats["_id"]
                stats["schema_name"] = self.schema_name
                return stats
            else:
                return {
                    "schema_name": self.schema_name,
                    "total_records": 0,
                    "created_today": 0,
                    "updated_today": 0
                }
            
        except Exception as e:
            logger.error(f"Failed to get schema stats for {self.schema_name}: {e}")
            raise DatabaseError(f"Failed to get schema stats: {e}")
    
    async def delete_all_by_schema(self) -> int:
        """
        Delete all records for a schema.
        
        Args:
            schema_name: Schema name
            
        Returns:
            int: Number of deleted records
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            result = await self._get_collection().delete_many({"schema_name": self.schema_name})
            logger.info(f"Deleted {result.deleted_count} records for schema: {self.schema_name}")
            return result.deleted_count
            
        except Exception as e:
            logger.error(f"Failed to delete records for schema {self.schema_name}: {e}")
            raise DatabaseError(f"Failed to delete records for schema: {e}")
