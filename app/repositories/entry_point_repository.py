from  datetime import datetime, timezone
from typing import Optional
import uuid

from sqlalchemy import select
from app.models.workflow import WorkFlowEntryPoint, WorkflowExecution
from sqlalchemy.ext.asyncio import AsyncSession
from app.repositories.base_repository import BaseRepository
from app.schemas.workflow import WorkFlowEntryPointCreate, WorkFlowEntryPointUpdate


class EntryPointRepository(BaseRepository[WorkFlowEntryPoint, WorkFlowEntryPointCreate, WorkFlowEntryPointUpdate]):
    """Repository for managing workflow executions."""

    def __init__(self, session: AsyncSession):
        super().__init__(WorkFlowEntryPoint, session)

    async def get_by_uid(self, uid: uuid.UUID) -> Optional[WorkflowExecution]:
        """
        Get workflow by UID.
        
        Args:
            uid: Workflow UID
            
        Returns:
            Optional[WorkflowExecution]: Workflow instance or None
        """
        query = select(WorkflowExecution).where(WorkflowExecution.id == uid)
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def find_by_trigger(self, trigger_type: str, trigger_value: str):
        """ Find entry points by trigger type and value. """
        result = await self.db.execute(
            select(WorkFlowEntryPoint).where(
                WorkFlowEntryPoint.trigger_type == trigger_type,
                WorkFlowEntryPoint.trigger_value == trigger_value
            )
        )
        return result.scalars().all()
