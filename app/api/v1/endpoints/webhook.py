"""
Webhook API endpoints for handling incoming webhook requests.

This module provides endpoints for webhook functionality including
webhook registration, handling incoming requests, and authentication.
"""

from datetime import datetime
from typing import Dict, Any, Optional
import uuid

from fastapi import APIRouter, Request, HTTPException, Depends, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from app.core.database import get_db
from app.schemas.workflow import WorkFlowEntryStartData
from app.utils.exceptions import NotFoundError
from app.utils.logging import get_logger


# Set up logging
logger = get_logger("app.api.webhook")

router = APIRouter()


class WebhookResponse(BaseModel):
    """Response model for webhook operations."""
    message: str
    webhook_id: Optional[str] = None
    timestamp: datetime


class WebhookRegistration(BaseModel):
    """Model for webhook registration."""
    webhook_path: str
    node_id: str
    workflow_id: str
    http_methods: list[str]
    authentication_required: bool = False


# In-memory webhook registry (in production, this would be in a database)
webhook_registry: Dict[str, Dict[str, Any]] = {
    "example_webhook": {
        "webhook_id": "example_123",
        "node_id": "node_1",
        "workflow_id": "1efc6abc-96fb-46fb-8d27-b521720b5b93",
        "http_methods": ["POST"],
        "authentication_required": True,
        "authentication": {
            "method": "basic",
            "username": "webhook_user",
            "password": "secure_password",
        },
        "created_at": datetime.utcnow(),
        "active": True
    }
}


@router.post("/register")
async def register_webhook(
    registration: WebhookRegistration,
    db=Depends(get_db)
) -> WebhookResponse:
    """
    Register a new webhook endpoint.
    
    Args:
        registration: Webhook registration data
        db: Database session
        
    Returns:
        WebhookResponse: Registration confirmation
    """
    try:
        webhook_id = f"{registration.workflow_id}_{registration.node_id}"
        
        # Store webhook configuration
        webhook_registry[registration.webhook_path] = {
            "webhook_id": webhook_id,
            "node_id": registration.node_id,
            "workflow_id": registration.workflow_id,
            "http_methods": registration.http_methods,
            "authentication_required": registration.authentication_required,
            "created_at": datetime.utcnow(),
            "active": True
        }
        
        logger.info(f"Webhook registered: {registration.webhook_path} -> {webhook_id}")
        
        return WebhookResponse(
            message="Webhook registered successfully",
            webhook_id=webhook_id,
            timestamp=datetime.utcnow()
        )
        
    except Exception as e:
        logger.error(f"Failed to register webhook: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to register webhook"
        )


@router.delete("/unregister/{webhook_path}")
async def unregister_webhook(
    webhook_path: str,
    db=Depends(get_db)
) -> WebhookResponse:
    """
    Unregister a webhook endpoint.
    
    Args:
        webhook_path: Path of the webhook to unregister
        db: Database session
        
    Returns:
        WebhookResponse: Unregistration confirmation
    """
    try:
        if webhook_path in webhook_registry:
            del webhook_registry[webhook_path]
            logger.info(f"Webhook unregistered: {webhook_path}")
            
            return WebhookResponse(
                message="Webhook unregistered successfully",
                timestamp=datetime.utcnow()
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Webhook not found"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to unregister webhook: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to unregister webhook"
        )

# @router.api_route("/trigger/test/{webhook_path}", methods=["GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"])
# async def handle_test_webhook(
#     webhook_path: str,
#     request: Request,
#     db=Depends(get_db)
# ):
#     """
#     Handle incoming webhook requests.
    
#     Args:
#         webhook_path: The webhook path
#         request: FastAPI request object
#         db: Database session
        
#     Returns:
#         Response based on webhook configuration
#     """
#     try:
#         # Check if webhook is registered
#         if webhook_path not in webhook_registry:
#             logger.warning(f"Webhook not found: {webhook_path}")
#             raise HTTPException(
#                 status_code=status.HTTP_404_NOT_FOUND,
#                 detail="Webhook not found"
#             )
        
#         webhook_config = webhook_registry[webhook_path]
        
#         # move to node run()
#         # Validate HTTP method
#         allowed_method = webhook_config.get("http_method", "GET")
#         if request.method != allowed_method:
#             logger.warning(f"Method not allowed for webhook {webhook_path}: {request.method}")
#             raise HTTPException(
#                 status_code=status.HTTP_405_METHOD_NOT_ALLOWED,
#                 detail=f"Method {request.method} not allowed"
#             )
        
#         # Authenticate request if required
#         if webhook_config.get("authentication_required", False):
#             if not await authenticate_webhook(request, webhook_config):
#                 logger.warning(f"Authentication failed for webhook: {webhook_path}")
#                 raise HTTPException(
#                     status_code=status.HTTP_401_UNAUTHORIZED,
#                     detail="Authentication failed"
#                 )
        
#         # Read request body
#         body = await request.body()
        
#         # Prepare webhook data
#         webhook_data = {
#             "webhook_path": webhook_path,
#             "method": request.method,
#             "headers": dict(request.headers),
#             "query": dict(request.query_params),
#             "body": parse_request_body(request, body),
#             "client_ip": get_client_ip(request),
#             "timestamp": datetime.utcnow().isoformat(),
#             "webhook_id": webhook_config["webhook_id"],
#             "node_id": webhook_config["node_id"],
#             "workflow_id": webhook_config["workflow_id"]
#         }
        
#         # Trigger workflow execution with webhook data
#         from app.services.workflow_service import WorkFlowService
        
#         try:
#             # Get workflow ID from webhook config
#             workflow_id = webhook_config["workflow_id"]
            
#             # Initialize workflow service and start the workflow
#             workflow_service = WorkFlowService(db)
#             execution_result = await workflow_service.start_workflow_by_id(
#                 workflow_id=workflow_id,
#                 input_data={
#                     "webhook_data": webhook_data,
#                     "node_id": webhook_config["node_id"]
#                 }
#             )
            
#             logger.info(
#                 f"Webhook triggered workflow execution",
#                 webhook_path=webhook_path,
#                 client_ip=webhook_data['client_ip'],
#                 workflow_id=workflow_id,
#                 execution_id=execution_result.get("execution_id")
#             )
            
#             # Return success response
#             return JSONResponse(
#                 status_code=200,
#                 content={
#                     "message": "Webhook received and workflow triggered successfully",
#                     "webhook_id": webhook_config["webhook_id"],
#                     "workflow_execution_id": execution_result.get("execution_id"),
#                     "timestamp": webhook_data["timestamp"]
#                 }
#             )
            
#         except Exception as e:
#             logger.error(
#                 f"Failed to trigger workflow from webhook: {str(e)}",
#                 webhook_path=webhook_path,
#                 workflow_id=webhook_config.get("workflow_id"),
#                 error=str(e)
#             )
            
#             # Return success for the webhook but include error about workflow
#             return JSONResponse(
#                 status_code=202,
#                 content={
#                     "message": "Webhook received but workflow execution failed",
#                     "webhook_id": webhook_config["webhook_id"],
#                     "error": str(e),
#                     "timestamp": webhook_data["timestamp"]
#                 }
#             )
        
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"Error handling webhook {webhook_path}: {str(e)}", exc_info=True)
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail="Internal server error"
#         )


@router.api_route("/trigger/{webhook_path}", methods=["GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"])
async def handle_webhook(
    webhook_path: str,
    request: Request,
    db=Depends(get_db)
):
    """
    Handle incoming webhook requests.
    
    Args:
        webhook_path: The webhook path
        request: FastAPI request object
        db: Database session
        
    Returns:
        Response based on webhook configuration
    """
    try:
        from app.services.workflow_service import WorkFlowService
        
        workflow_service = WorkFlowService(db)
        workflow_entry_data = WorkFlowEntryStartData(
            execution_id=uuid.uuid4(),
            trigger_type='webhook',
            trigger_value=webhook_path,
            input_data=request
        )
        execution_id = await workflow_service.start_workflow(workflow_entry_data)
        if execution_id:
            return JSONResponse(
                status_code=200,
                content={
                    "message": "Webhook received and workflow triggered successfully"
                }
            )
        else:
            return HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Webhook not found"
            )
                

        # webhook_config = webhook_registry[webhook_path]

        # # Check if webhook is active
        # if not webhook_config.get("active", True):
        #     logger.warning(f"Webhook inactive: {webhook_path}")
        #     raise HTTPException(
        #         status_code=status.HTTP_410_GONE,
        #         detail="Webhook is no longer active"
        #     )

    except NotFoundError as e:
        logger.warning(
            "No workflow entry points found",
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"No workflow entry points found for trigger_type=webhook and trigger_value={webhook_path}"
        )  
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error handling webhook {webhook_path}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )
