"""
API endpoints for dynamic record management.
"""

from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.api.deps import get_current_active_user
from app.core.database import get_mongodb
from app.models.user import User
from app.schemas.common import SuccessResponse
from app.schemas.dynamic_record import (
    DynamicRecordCreate, DynamicRecordUpdate, DynamicR<PERSON>ord, DynamicRecordList,
    DynamicRecordQuery, DynamicRecordBulkCreate, DynamicRecordBulkResponse, DynamicRecordStats, RecordSource
)
from app.services.dynamic_record_service import DynamicRecordService
from app.services.schema_service import SchemaService
from app.utils.exceptions import NotFoundError, ValidationError

router = APIRouter()


async def get_record_service(db: AsyncIOMotorDatabase = Depends(get_mongodb), schema_name: str = "") -> DynamicRecordService:
    """Dependency to get dynamic record service."""
    schema_service = SchemaService(db)
    return DynamicRecordService(db, schema_service, schema_name=schema_name)


@router.post(
    "/{schema_name}",
    response_model=Any,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new record",
    description="Create a new record in the specified schema with data validation."
)
async def create_record(
    schema_name: str,
    data: Dict[str, Any],
    current_user: User = Depends(get_current_active_user),
    record_service: DynamicRecordService = Depends(get_record_service)
) -> Any:
    """
    Create a new record in a schema.
    
    - **schema_name**: The name of the schema
    - **data**: Record data that must conform to the schema
    
    The data will be validated against the schema definition.
    Required fields must be provided, and all data types must match.
    """
    try:
        record_data = DynamicRecordCreate(
            schema_name=schema_name,
            data=data,
            created_by=current_user.id,
            created_by_source=RecordSource.USER
        )
        return await record_service.create_record(record_data)
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )


@router.get(
    "/{schema_name}",
    response_model=DynamicRecordList,
    summary="Get records",
    description="Retrieve records from a schema with filtering, sorting, and pagination."
)
async def get_records(
    schema_name: str,
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Page size"),
    sort_by: Optional[str] = Query(None, description="Field to sort by"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="Sort order"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncIOMotorDatabase = Depends(get_mongodb),
) -> DynamicRecordList:
    """
    Get records from a schema with pagination and sorting.
    
    - **schema_name**: The name of the schema
    - **page**: Page number (starts from 1)
    - **page_size**: Number of records per page (1-100)
    - **sort_by**: Field name to sort by (defaults to created_at)
    - **sort_order**: Sort order - 'asc' or 'desc'
    
    For advanced filtering, use the POST /records/{schema_name}/query endpoint.
    """
    try:
        schema_service = SchemaService(db)
        record_service = DynamicRecordService(db, schema_service, schema_name=schema_name)
        query = DynamicRecordQuery(
            schema_name=schema_name,
            page=page,
            page_size=page_size,
            sort_by=sort_by,
            sort_order=sort_order,
            filters=[]
        )
        
        query.schema_name = schema_name
        return await record_service.get_records(query)
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.post(
    "/{schema_name}/query",
    response_model=DynamicRecordList,
    summary="Query records with filters",
    description="Query records with advanced filtering, sorting, and pagination."
)
async def query_records(
    schema_name: str,
    query: DynamicRecordQuery,
    current_user: User = Depends(get_current_active_user),
    db: AsyncIOMotorDatabase = Depends(get_mongodb),
) -> DynamicRecordList:
    """
    Query records with advanced filtering.
    
    - **schema_name**: The name of the schema
    - **filters**: List of filter conditions
    - **sort_by**: Field to sort by
    - **sort_order**: Sort order ('asc' or 'desc')
    - **page**: Page number
    - **page_size**: Page size
    
    Filter operators:
    - eq: equals
    - ne: not equals
    - gt: greater than
    - gte: greater than or equal
    - lt: less than
    - lte: less than or equal
    - in: value in list
    - nin: value not in list
    - contains: string contains (case-insensitive)
    - regex: regular expression match
    """
    try:
        schema_service = SchemaService(db)
        record_service = DynamicRecordService(db, schema_service, schema_name=schema_name)
        # query.schema_name = schema_name
        return await record_service.get_records(query)
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )

@router.get(
    "/daily/stats/{schema_name}",
    response_model=DynamicRecordStats,
    summary="Get schema statistics",
    description="Get statistics about records in a schema."
)
async def get_schema_stats(
    schema_name: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncIOMotorDatabase = Depends(get_mongodb),
) -> DynamicRecordStats:
    """
    Get statistics for records in a schema.
    
    - **schema_name**: The name of the schema
    
    Returns statistics including total record count, records created today,
    and records updated today.
    """
    try:
        schema_service = SchemaService(db)
        record_service = DynamicRecordService(db, schema_service, schema_name=schema_name)
        return await record_service.get_schema_stats(schema_name)
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.get(
    "/{schema_name}/{record_id}",
    response_model=Dict[str, Any],
    summary="Get record by ID",
    description="Retrieve a specific record by its ID."
    
)
async def get_record_by_id(
    schema_name: str,
    record_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncIOMotorDatabase = Depends(get_mongodb),
) -> Dict[str, Any]:
    """
    Get a record by its ID.
    
    - **schema_name**: The name of the schema
    - **record_id**: The unique identifier of the record
    """
    try:
        schema_service = SchemaService(db)
        record_service = DynamicRecordService(db, schema_service, schema_name=schema_name)
        rec = await record_service.get_record_by_id(record_id)
        return rec
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.patch(
    "/{schema_name}/{record_id}",
    response_model=DynamicRecord,
    summary="Update record",
    description="Update an existing record with new data."
)
async def update_record(
    schema_name: str,
    record_id: str,
    data: Dict[str, Any],
    current_user: User = Depends(get_current_active_user),
    db: AsyncIOMotorDatabase = Depends(get_mongodb),
) -> Dict[str, Any]:
    """
    Update an existing record.
    
    - **schema_name**: The name of the schema
    - **record_id**: The unique identifier of the record
    - **data**: Updated record data
    
    This performs a partial update - only provided fields will be updated.
    The updated data will be validated against the schema.
    """
    try:
        schema_service = SchemaService(db)
        record_service = DynamicRecordService(db, schema_service, schema_name=schema_name)
        update_data = DynamicRecordUpdate(data=data)
        update_data.data["_updated_by"] = current_user.id
        update_data.data["_updated_by_source"] = "user"
        return await record_service.update_record(record_id, update_data)
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )


@router.delete(
    "/{schema_name}/{record_id}",
    response_model=SuccessResponse,
    summary="Delete record",
    description="Delete a specific record."
)
async def delete_record(
    schema_name: str,
    record_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncIOMotorDatabase = Depends(get_mongodb),
) -> SuccessResponse:
    """
    Delete a record.
    
    - **schema_name**: The name of the schema
    - **record_id**: The unique identifier of the record
    """
    try:
        schema_service = SchemaService(db)
        record_service = DynamicRecordService(db, schema_service, schema_name=schema_name)
        deleted = await record_service.delete_record(record_id)
        if not deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Record not found"
            )
        
        return SuccessResponse(message="Record deleted successfully")
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )

# TODO : Nkkk for future  reference
# @router.post(
#     "/{schema_name}/validate",
#     response_model=DynamicRecordValidationResponse,
#     summary="Validate record data",
#     description="Validate record data against schema without creating the record."
# )
# async def validate_record_data(
#     schema_name: str,
#     data: Dict[str, Any],
#     current_user: User = Depends(get_current_active_user),
#     db: AsyncIOMotorDatabase = Depends(get_mongodb),
# ) -> DynamicRecordValidationResponse:
#     """
#     Validate record data against schema.
    
#     - **schema_name**: The name of the schema
#     - **data**: Record data to validate
    
#     This endpoint allows you to validate data before creating or updating records,
#     helping to catch validation errors early.
#     """
#     try:
#         schema_service = SchemaService(db)
#         record_service = DynamicRecordService(db, schema_service, schema_name=schema_name)
#         return await record_service.validate_record_data(data)
#     except NotFoundError as e:
#         raise HTTPException(
#             status_code=status.HTTP_404_NOT_FOUND,
#             detail=str(e)
#         )


@router.post(
    "/{schema_name}/bulk",
    response_model=DynamicRecordBulkResponse,
    summary="Bulk create records",
    description="Create multiple records in a single operation."
)
async def bulk_create_records(
    schema_name: str,
    records: List[Dict[str, Any]],
    current_user: User = Depends(get_current_active_user),
    db: AsyncIOMotorDatabase = Depends(get_mongodb),
) -> DynamicRecordBulkResponse:
    """
    Create multiple records in bulk.
    
    - **schema_name**: The name of the schema
    - **records**: List of record data objects
    
    Each record will be validated individually. Successfully validated records
    will be created, while invalid records will be reported in the response.
    Maximum 100 records per request.
    """
    try:
        schema_service = SchemaService(db)
        record_service = DynamicRecordService(db, schema_service, schema_name=schema_name)
        
        if len(records) > 100:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Maximum 100 records allowed per bulk operation"
            )
        
        bulk_data = DynamicRecordBulkCreate(
            schema_name=schema_name,
            records=records,
        )
        return await record_service.bulk_create_records(bulk_data)
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )

# TODO : Nkkk for future  reference

# TODO : Nkkk for future  reference

# @router.delete(
#     "/{schema_name}/all",
#     response_model=SuccessResponse,
#     summary="Delete all records",
#     description="Delete all records in a schema."
# )
# async def delete_all_records(
#     schema_name: str,
#     current_user: User = Depends(get_current_active_user),
#     record_service: DynamicRecordService = Depends(get_record_service)
# ) -> SuccessResponse:
#     """
#     Delete all records in a schema.
    
#     - **schema_name**: The name of the schema
    
#     WARNING: This operation cannot be undone. All records in the schema will be permanently deleted.
#     """
#     try:
#         deleted_count = await record_service.delete_all_records(schema_name)
#         return SuccessResponse(message=f"Deleted {deleted_count} records successfully")
#     except NotFoundError as e:
#         raise HTTPException(
#             status_code=status.HTTP_404_NOT_FOUND,
#             detail=str(e)
#         )
