"""
API endpoints for dynamic schema management.
"""

from typing import Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.api.deps import get_current_active_user
from app.core.database import get_mongodb
from app.models.user import User
from app.schemas.common import SuccessResponse
from app.schemas.schema import (
    SchemaDefinitionCreate, SchemaDefinitionCreateDB, SchemaDefinitionUpdate, SchemaDefinition, SchemaDefinitionList, SchemaField,
    SchemaValidationResponse, RecordSource
)
from app.services.schema_service import SchemaService
from app.utils.exceptions import (
    NotFoundError, ValidationError, ConflictError
)

router = APIRouter()


async def get_schema_service(db: AsyncIOMotorDatabase = Depends(get_mongodb)) -> SchemaService:
    """Dependency to get schema service."""
    return SchemaService(db)


@router.post(
    "/",
    response_model=SchemaDefinition,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new schema",
    description="Create a new dynamic schema with field definitions and validation rules."
)
async def create_schema(
    schema_data: SchemaDefinitionCreate,
    current_user: User = Depends(get_current_active_user),
    schema_service: SchemaService = Depends(get_schema_service)
) -> SchemaDefinition:
    """
    Create a new dynamic schema.
    
    - **name**: Unique schema name (lowercase, alphanumeric with underscores)
    - **display_name**: Human-readable schema name
    - **description**: Optional schema description
    - **fields**: List of field definitions with validation rules
    
    The schema name must be unique and follow naming conventions.
    At least one field must be marked as required.
    """
    try:
        # Set the creator
        schema_data_db = SchemaDefinitionCreateDB(
            **schema_data.model_dump(),  # Unpack existing schema data
            created_by=str(current_user.id),
            created_by_source=RecordSource.USER
        )
        return await schema_service.create_schema_definition(schema_data_db)
    except ConflictError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )


@router.get(
    "/",
    response_model=SchemaDefinitionList,
    summary="Get schemas",
    description="Retrieve a list of schemas with optional filtering and pagination."
)
async def get_schemas(
    skip: int = Query(0, ge=0, description="Number of schemas to skip"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of schemas to return"),
    created_by: Optional[int] = Query(None, description="Filter by creator user ID"),
    include_system: bool = Query(True, description="Include system schemas"),
    current_user: User = Depends(get_current_active_user),
    schema_service: SchemaService = Depends(get_schema_service)
) -> SchemaDefinitionList:
    """
    Get a list of schemas with pagination and filtering.
    
    - **skip**: Number of schemas to skip for pagination
    - **limit**: Maximum number of schemas to return (1-100)
    - **created_by**: Optional filter by creator user ID
    - **include_system**: Whether to include system-defined schemas
    """
    return await schema_service.get_schemas(
        skip=skip,
        limit=limit,
        created_by=created_by,
        include_system=include_system
    )


@router.get(
    "/{schema_id}",
    response_model=SchemaDefinition,
    summary="Get schema by ID",
    description="Retrieve a specific schema by its ID."
)
async def get_schema_by_id(
    schema_id: str,
    current_user: User = Depends(get_current_active_user),
    schema_service: SchemaService = Depends(get_schema_service)
) -> SchemaDefinition:
    """
    Get a schema by its ID.
    
    - **schema_id**: The unique identifier of the schema
    """
    try:
        return await schema_service.get_schema_by_id(schema_id)
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.get(
    "/name/{schema_name}",
    response_model=SchemaDefinition,
    summary="Get schema by name",
    description="Retrieve a specific schema by its name."
)
async def get_schema_by_name(
    schema_name: str,
    current_user: User = Depends(get_current_active_user),
    schema_service: SchemaService = Depends(get_schema_service)
) -> SchemaDefinition:
    """
    Get a schema by its name.
    
    - **schema_name**: The unique name of the schema
    """
    try:
        return await schema_service.get_schema_by_name(schema_name, use_cache=True)
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


# @router.put(
#     "/{schema_id}",
#     response_model=SchemaDefinition,
#     summary="Update schema",
#     description="Update an existing schema. System schemas have restrictions on modifications."
# )
# async def update_schema(
#     schema_id: str,
#     update_data: SchemaDefinitionUpdate,
#     current_user: User = Depends(get_current_active_user),
#     schema_service: SchemaService = Depends(get_schema_service)
# ) -> SchemaDefinition:
#     """
#     Update an existing schema.
    
#     - **schema_id**: The unique identifier of the schema
#     - **display_name**: Updated human-readable name (optional)
#     - **description**: Updated description (optional)
#     - **fields**: Updated field definitions (optional)
    
#     Note: System schemas (like Contact) have restrictions:
#     - Default fields cannot have their type or required status changed
#     - New fields can be added but default fields cannot be removed
#     """
#     try:
#         # Set the updater
#         update_data.updated_by = current_user.id
        
#         return await schema_service.update_schema(schema_id, update_data)
#     except NotFoundError as e:
#         raise HTTPException(
#             status_code=status.HTTP_404_NOT_FOUND,
#             detail=str(e)
#         )
#     except ValidationError as e:
#         raise HTTPException(
#             status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
#             detail=str(e)
#         )

@router.patch(
    "/{schema_id}",
    response_model=SchemaDefinition,
    summary="Partially update schema",
    description="Partially update an existing schema. Only provided fields will be updated."
)
async def patch_schema(
    schema_id: str,
    update_data: SchemaDefinitionUpdate,
    current_user: User = Depends(get_current_active_user),
    schema_service: SchemaService = Depends(get_schema_service)
) -> SchemaDefinition:
    """
    Partially update an existing schema.
    
    - **schema_id**: The unique identifier of the schema
    - **display_name**: Updated human-readable name (optional)
    - **description**: Updated description (optional)
    - **fields**: Updated field definitions (optional)
    
    Note: This endpoint allows partial updates, only modifying the fields that are provided.
    System schemas (like Contact) have restrictions:
    - Default fields cannot have their type or required status changed
    - New fields can be added but default fields cannot be removed
    """
    try:
        # Set the updater
        update_data.updated_by = current_user.id
        return await schema_service.update_schema(schema_id, update_data)
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except ConflictError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )


@router.delete(
    "/{schema_id}",
    response_model=SuccessResponse,
    summary="Delete schema",
    description="Delete a schema. System schemas cannot be deleted."
)
async def delete_schema(
    schema_id: str,
    current_user: User = Depends(get_current_active_user),
    schema_service: SchemaService = Depends(get_schema_service)
) -> SuccessResponse:
    """
    Delete a schema.
    
    - **schema_id**: The unique identifier of the schema
    
    Note: System schemas (like Contact) cannot be deleted.
    This operation will also delete all associated records.
    """
    try:
        deleted = await schema_service.delete_schema(schema_id)
        if not deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Schema not found"
            )
        
        return SuccessResponse(message="Schema deleted successfully")
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )


@router.post(
    "/validate",
    response_model=SchemaValidationResponse,
    summary="Validate schema definition",
    description="Validate schema field definitions without creating the schema."
)
async def validate_schema_definition(
    fields: list[SchemaField],
    current_user: User = Depends(get_current_active_user),
    schema_service: SchemaService = Depends(get_schema_service)
) -> SchemaValidationResponse:
    """
    Validate schema field definitions.
    
    - **fields**: List of field definitions to validate
    
    This endpoint allows you to validate schema definitions before creating them,
    helping to catch validation errors early in the development process.
    """
    return await schema_service.validate_schema_definition(fields)


@router.get(
    "/{schema_name}/fields",
    response_model=list[SchemaField],
    summary="Get schema fields",
    description="Get the field definitions for a specific schema."
)
async def get_schema_fields(
    schema_name: str,
    current_user: User = Depends(get_current_active_user),
    schema_service: SchemaService = Depends(get_schema_service)
) -> list[SchemaField]:
    """
    Get schema field definitions.
    
    - **schema_name**: The name of the schema
    
    Returns the complete field definitions including validation rules,
    which can be used for dynamic form generation or data validation.
    """
    try:
        return await schema_service.get_schema_fields(schema_name)
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.post(
    "/cache/clear",
    response_model=SuccessResponse,
    summary="Clear schema cache",
    description="Clear the schema cache to force reload from database."
)
async def clear_schema_cache(
    current_user: User = Depends(get_current_active_user),
    schema_service: SchemaService = Depends(get_schema_service)
) -> SuccessResponse:
    """
    Clear the schema cache.
    
    This endpoint clears the in-memory schema cache, forcing the next
    schema requests to reload from the database. Useful for development
    or when schemas are modified outside the API.
    """
    await schema_service.clear_cache()
    return SuccessResponse(message="Schema cache cleared successfully")
