from typing import Dict, Any, List
from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import RedirectResponse
from fastapi.responses import RedirectResponse
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import APIRouter, HTTPException, Query

from app.credential.utils.credential_manager import CredentialManager
from app.credential.utils.credential_registry import CredentialRegistry
from app.credential.base.credential_model import CredentialRequestModel, CredentialUpdateDB, CredentialUpdateModel
from app.models.user import User
from app.repositories.credential_repository import CredentialRepository
from app.api.deps import get_current_active_user, get_db


router = APIRouter()

@router.get("/", response_model=list[dict])
async def get_credentials():
    return CredentialRegistry.get_all_credentials()

@router.post("/", response_model=Dict[str, Any])
async def create_credential(
    credential_request: CredentialRequestModel,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Create a new credential.
    
    This endpoint validates the credential data using the credential manager,
    encrypts sensitive information, and stores it in the database.
    
    Args:
        credential_request: The credential request model with all required parameters
        db: Database session
        
    Returns:
        Dictionary with credential information and status
    """
    try:
        # Initialize repository
        credential_repo = CredentialRepository(db)
        
        # Add credential to database
        result = await credential_repo.add_credential(
            credential_request=credential_request,
            created_by=current_user.id  # This should come from the authenticated user
        )
        
        return {
            "status": "success",
            "message": "Credential created successfully",
            "credential": result.to_dict(sanitize_sensitive=True)  # Convert model to dict
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create credential: {str(e)}")

@router.get("/type/{credential_type}", response_model=List[Dict[str, Any]])
async def get_credentials_by_type(
    credential_type: str,
    db: AsyncSession = Depends(get_db)
) -> List[Dict[str, Any]]:
    """
    Get all credentials of a specific type.
    
    Args:
        credential_type: The type of credential to retrieve (e.g., "whatsapp_api")
        db: Database session
        
    Returns:
        List of credential details
    """
    try:
        # Initialize repository
        credential_repo = CredentialRepository(db)
        
        # Get credentials from database
        credentials = await credential_repo.get_by_type(credential_type)
        
        # Return sanitized credential data
        return [credential.to_dict(sanitize_sensitive=True) for credential in credentials]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve credentials: {str(e)}")

@router.get("/test/{credential_id}", response_model=Dict[str, Any])
async def test_credential_by_id(
    credential_id: str,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Test an existing credential using its ID.
    
    This endpoint fetches the credential from the database, 
    decrypts its data, reconstructs the credential request model,
    and tests it by making a request to the target API.
    
    Args:
        credential_id: The UUID of the credential to test
        db: Database session
        
    Returns:
        Dictionary with test result status
    """
    try:
        import uuid
        from app.credential.utils.credential_manager import CredentialManager
        
        # Parse the string to UUID
        try:
            credential_uuid = uuid.UUID(credential_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid credential ID format")

        # Initialize credential manager
        credential_manager = CredentialManager(db = db)

        result = await credential_manager.test_credential(credential_uuid)

        return {
            "succes": result,
            "message": "Credential test successful" if result else "Credential test failed",
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException as e:
        raise e
    except Exception as e:
        # Log the detailed error
        # import traceback
        # traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to test credential: {str(e)}")
    
@router.post("/authorize", response_model=Dict[str, Any])
async def generate_oauth2_authurl(
    credential_request: CredentialRequestModel,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)  # Add this parameter to receive the request data
) -> Dict[str, Any]:
    """
    Generate OAuth2 authorization URL and optionally redirect to it.

    Args:
        node_request: NodeRequest containing OAuth2 parameters
    1. Uses CredentialManager to call the corresponding credential function
    2. Calls the specified function to generate the authorization URL
    3. Returns the authorization URL or redirects to it 

    Returns:
        Dict containing the authorization URL
    """
    try:
        credential_manager = CredentialManager(db=db)
        return await credential_manager.save_credential(credential_request, created_by=current_user.id)
    
    except ValueError as exc:
        raise HTTPException(status_code=400, detail=str(exc))
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/callback", response_model=Any)
async def get_oauth2_callback(
    code: str = Query(None, description="Authorization code from OAuth2 provider"),
    state: str = Query(None, description="State parameter containing encoded credentials"),
    error: str = Query(None, description="Error parameter if authorization failed"),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Handle OAuth2 callback and exchange authorization code for tokens.
    """
    try:        
        if error:
            raise HTTPException(status_code=400, detail=f"OAuth2 authorization failed: {error}")

        if not code:
            raise HTTPException(status_code=400, detail="Authorization code is required")

        if not state:
            raise HTTPException(status_code=400, detail="State parameter is required")
            
        credential_manager = CredentialManager(db)
        result = await credential_manager.get_oauth2_callback(code, state)
        redirect_uri = result["tokens"].get("redirect_uri")
        if not redirect_uri:
            raise HTTPException(status_code=500, detail="No redirect URL provided after OAuth2 process")
        return RedirectResponse(url=redirect_uri)

        redirect_uri = result["tokens"].get("redirect_uri")
        if not redirect_uri:
            raise HTTPException(status_code=500, detail="No redirect URL provided after OAuth2 process")
        return RedirectResponse(url=redirect_uri)

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"OAuth2 callback error: {str(e)}")
    
@router.patch("/authorize/{credential_id}", response_model=Dict[str, Any])
async def update_oauth2_credential(
    credential_id: str,
    update_request: CredentialUpdateModel,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)  # Add this parameter to receive the request data
) -> Dict[str, Any]:
    """
    Update an existing OAuth2 credential
    """
    try:
        import uuid
        credential_manager = CredentialManager(db=db)
        # Parse the UUID
        try:
            uuid_obj = uuid.UUID(credential_id)
        except ValueError:
            raise ValueError(f"Invalid credential ID format: {credential_id}")
        
        # Set the ID in the model_dump before creating CredentialUpdateDB
        data = update_request.model_dump()
        data["id"] = str(uuid_obj)  # Add the ID to the data dictionary
        # Create CredentialUpdateDB with ID included
        request_data = CredentialUpdateDB(**data)
        return await credential_manager.save_credential(request_data, created_by=current_user.id)

    except ValueError as exc:
        raise HTTPException(status_code=400, detail=str(exc))
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))    
    
@router.get("/{credential_id}", response_model=Dict[str, Any])
async def get_credential(
    credential_id: str,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get a credential by ID.
    
    Args:
        credential_id: The UUID of the credential
        db: Database session
        
    Returns:
        Credential details
    """
    try:
        import uuid
        # Parse the string to UUID
        try:
            credential_uuid = uuid.UUID(credential_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid credential ID format")
            
        # Initialize repository
        credential_repo = CredentialRepository(db)
        
        # Get credential from database
        credential = await credential_repo.get_by_id(credential_uuid)
        if not credential:
            raise HTTPException(status_code=404, detail=f"Credential with ID {credential_id} not found")
            
        # Return sanitized credential data
        return credential.to_dict(sanitize_sensitive=True)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve credential: {str(e)}")
    
@router.patch("/{credential_id}", response_model=Dict[str, Any])
async def update_credential(
    credential_id: str,
    update_request: CredentialUpdateModel,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Update an existing credential.
    
    This endpoint validates the updated credential data using the credential manager,
    encrypts sensitive information, and updates it in the database.
    
    Args:
        credential_id: The UUID of the credential to update
        update_request: The credential update model with updated parameters
        current_user: The authenticated user making the request
        db: Database session
        
    Returns:
        Dictionary with credential information and status
    """
    try:
        import uuid
        
        # Initialize credential manager
        credential_manager = CredentialManager(db=db)
        
        # Parse the UUID
        try:
            uuid_obj = uuid.UUID(credential_id)
        except ValueError:
            raise ValueError(f"Invalid credential ID format: {credential_id}")
        
        # Set the ID in the request object
        update_data = update_request.model_dump()
        update_data["id"] = str(uuid_obj)
        
        # Create CredentialUpdateDB with ID included in the constructor
        request_data = CredentialUpdateDB(**update_data)
        # Use the credential manager to handle the update
        result = await credential_manager.update_credential(
            credential_request=request_data,
            updated_by=current_user.id
        )
        
        return {
            "result": result.to_dict(sanitize_sensitive=True)
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update credential: {str(e)}")