"""updated credential table

Revision ID: d1c8509df3ef
Revises: f8de8a4184f2
Create Date: 2025-07-31 17:44:06.643658

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'd1c8509df3ef'
down_revision = 'f8de8a4184f2'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # First, create the enum type if it doesn't exist
    op.execute("CREATE TYPE credentialstatus AS ENUM ('DRAFT', 'ACTIVE', 'DELETED', 'EXPIRED', 'REVOKED', 'FAILED')")
    
    # Add temporary columns to avoid constraints during migration
    op.add_column('credentials', sa.Column('created_by_id', sa.Integer(), nullable=True))
    op.add_column('credentials', sa.Column('updated_by_id', sa.Integer(), nullable=True))
    op.add_column('credentials', sa.Column('status_enum', sa.Enum('DRAFT', 'ACTIVE', 'DELETED', 'EXPIRED', 'REVOKED', 'FAILED', 
                                                                 name='credentialstatus', 
                                                                 native_enum=True), 
                                          nullable=True))
    
    # Update the temporary columns with data from existing columns
    op.execute("""
    UPDATE credentials 
    SET created_by_id = (SELECT id FROM users WHERE email = created_by LIMIT 1), 
        status_enum = status::credentialstatus
    """)
    
    # Set updated_by to same as created_by initially
    op.execute("UPDATE credentials SET updated_by_id = created_by_id")
    
    # Add foreign key constraints
    op.create_foreign_key('fk_credentials_created_by', 'credentials', 'users', ['created_by_id'], ['id'])
    op.create_foreign_key('fk_credentials_updated_by', 'credentials', 'users', ['updated_by_id'], ['id'])
    
    # Make columns not nullable
    op.alter_column('credentials', 'created_by_id', nullable=False)
    op.alter_column('credentials', 'updated_by_id', nullable=False)
    op.alter_column('credentials', 'status_enum', nullable=False)
    
    # Create indexes
    op.create_index('ix_credentials_created_by', 'credentials', ['created_by_id'])
    op.create_index('ix_credentials_updated_by', 'credentials', ['updated_by_id'])
    
    # Drop old columns
    op.drop_column('credentials', 'created_by')
    op.drop_column('credentials', 'status')
    
    # Rename new columns to original names
    op.alter_column('credentials', 'created_by_id', new_column_name='created_by')
    op.alter_column('credentials', 'updated_by_id', new_column_name='updated_by')
    op.alter_column('credentials', 'status_enum', new_column_name='status')
    
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    op.add_column('credentials', sa.Column('created_by_str', sa.VARCHAR(length=255), nullable=True))
    op.add_column('credentials', sa.Column('status_str', sa.VARCHAR(length=50), nullable=True))
    
    # Migrate data back from the enum to string fields
    op.execute("""
    UPDATE credentials 
    SET created_by_str = (SELECT email FROM users WHERE id = created_by),
        status_str = status::text
    """)
    
    # Make the new columns not nullable
    op.alter_column('credentials', 'created_by_str', nullable=False)
    op.alter_column('credentials', 'status_str', nullable=False)
    
    # Drop foreign key constraints
    op.drop_constraint('fk_credentials_created_by', 'credentials', type_='foreignkey')
    op.drop_constraint('fk_credentials_updated_by', 'credentials', type_='foreignkey')
    
    # Drop indexes
    op.drop_index('ix_credentials_created_by', table_name='credentials')
    op.drop_index('ix_credentials_updated_by', table_name='credentials')
    
    # Drop relationship columns
    op.drop_column('credentials', 'updated_by')
    op.drop_column('credentials', 'created_by')
    op.drop_column('credentials', 'status')
    
    # Rename columns back
    op.alter_column('credentials', 'created_by_str', new_column_name='created_by')
    op.alter_column('credentials', 'status_str', new_column_name='status')
    
    # Create old indexes if needed
    op.create_index('ix_credentials_status', 'credentials', ['status'], unique=False)
    
    # Drop the enum type
    op.execute("DROP TYPE credentialstatus")
